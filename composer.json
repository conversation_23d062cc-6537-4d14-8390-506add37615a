{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "ext-fileinfo": "*", "ext-simplexml": "*", "ext-zip": "*", "dompdf/dompdf": "^3.1", "google-gemini-php/laravel": "^1.0", "laravel/cashier": "^15.6", "laravel/framework": "^11.31", "laravel/horizon": "^5.31", "laravel/jetstream": "^5.3", "laravel/reverb": "^1.5", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "league/commonmark": "^2.4", "league/flysystem-aws-s3-v3": "^3.29", "livewire/livewire": "^3.0", "openai-php/laravel": "^0.11.0", "php-ffmpeg/php-ffmpeg": "^1.3", "phpoffice/phpword": "^1.3", "pusher/pusher-php-server": "^7.2", "setasign/fpdi": "^2.3", "tecnickcom/tcpdf": "^6.6"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pail": "^1.1", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail --timeout=0\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}