<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DocxJS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        #preview-container {
            border: 1px solid #ccc;
            min-height: 500px;
            margin-top: 20px;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DocxJS Test Page</h1>
        <p>This page tests the docx-preview library functionality.</p>
        
        <div>
            <input type="file" id="file-input" accept=".docx" />
            <button id="test-button">Test with Sample URL</button>
        </div>
        
        <div id="preview-container"></div>
    </div>

    <script src="https://unpkg.com/jszip/dist/jszip.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/docx-preview@0.3.5/dist/docx-preview.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DocxJS Test: Page loaded');
            
            // Handle file input
            document.getElementById('file-input').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    console.log('File selected:', file.name);
                    renderDocxPreview(file);
                }
            });
            
            // Test button with a sample URL
            document.getElementById('test-button').addEventListener('click', function() {
                alert('Testing with sample URL');
                // You would need to replace this with a valid DOCX URL in your system
                fetch('/sample.docx')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.blob();
                    })
                    .then(blob => {
                        renderDocxPreview(blob);
                    })
                    .catch(error => {
                        console.error('Error fetching sample file:', error);
                        document.getElementById('preview-container').innerHTML = 
                            `<div style="padding: 20px; color: red;">Error: ${error.message}</div>`;
                    });
            });
            
            function renderDocxPreview(docxData) {
                const container = document.getElementById('preview-container');
                container.innerHTML = '<div style="text-align: center; padding: 20px;">Loading preview...</div>';
                
                try {
                    docx.renderAsync(docxData, container)
                        .then(result => {
                            console.log('DocxJS: Rendering complete', result);
                        })
                        .catch(error => {
                            console.error('DocxJS: Rendering error', error);
                            container.innerHTML = 
                                `<div style="padding: 20px; color: red;">Rendering Error: ${error.message}</div>`;
                        });
                } catch (error) {
                    console.error('DocxJS: Exception during rendering setup', error);
                    container.innerHTML = 
                        `<div style="padding: 20px; color: red;">Setup Error: ${error.message}</div>`;
                }
            }
        });
    </script>
</body>
</html>
