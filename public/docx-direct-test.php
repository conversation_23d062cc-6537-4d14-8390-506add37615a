<?php
// This is a direct test page for docx-preview

// Get the URL parameter
$url = $_GET['url'] ?? '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DocxJS Direct Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        #preview-container {
            border: 1px solid #ccc;
            min-height: 600px;
            margin-top: 20px;
            padding: 10px;
        }
        .controls {
            margin-bottom: 20px;
        }
        button, input[type="text"] {
            padding: 8px 12px;
            margin-right: 10px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        input[type="text"] {
            width: 60%;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>DocxJS Direct Test Page</h1>
        <p>This page directly tests the docx-preview library without any Laravel/Livewire dependencies.</p>
        
        <div class="controls">
            <input type="text" id="url-input" placeholder="Enter document URL" value="<?php echo htmlspecialchars($url); ?>" style="width: 60%;">
            <button id="load-button">Load Document</button>
            <input type="file" id="file-input" accept=".docx">
        </div>
        
        <div class="status" id="status">Ready</div>
        
        <div id="preview-container">
            <p>Document preview will appear here.</p>
        </div>
    </div>

    <!-- Required libraries -->
    <script src="https://unpkg.com/jszip/dist/jszip.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/docx-preview@0.3.5/dist/docx-preview.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const urlInput = document.getElementById('url-input');
            const loadButton = document.getElementById('load-button');
            const fileInput = document.getElementById('file-input');
            const previewContainer = document.getElementById('preview-container');
            const statusElement = document.getElementById('status');
            
            // Check if docx library is available
            if (typeof docx === 'undefined') {
                statusElement.textContent = 'ERROR: docx-preview library not loaded!';
                statusElement.style.backgroundColor = '#ffcccc';
                return;
            } else {
                statusElement.textContent = 'docx-preview library loaded successfully.';
                statusElement.style.backgroundColor = '#ccffcc';
            }
            
            // Load from URL
            loadButton.addEventListener('click', function() {
                const url = urlInput.value.trim();
                if (!url) {
                    statusElement.textContent = 'Please enter a URL';
                    statusElement.style.backgroundColor = '#ffffcc';
                    return;
                }
                
                loadDocumentFromUrl(url);
            });
            
            // Load from file input
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    statusElement.textContent = `Loading file: ${file.name}`;
                    renderDocxPreview(file);
                }
            });
            
            // Auto-load if URL is provided
            if (urlInput.value.trim()) {
                loadDocumentFromUrl(urlInput.value.trim());
            }
            
            function loadDocumentFromUrl(url) {
                statusElement.textContent = `Loading document from URL: ${url}`;
                previewContainer.innerHTML = '<div style="text-align: center; padding: 20px;">Loading document...</div>';
                
                fetch(url)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Failed to fetch document: ${response.status} ${response.statusText}`);
                        }
                        statusElement.textContent = 'Document fetched successfully, rendering...';
                        return response.blob();
                    })
                    .then(blob => {
                        renderDocxPreview(blob);
                    })
                    .catch(error => {
                        statusElement.textContent = `Error: ${error.message}`;
                        statusElement.style.backgroundColor = '#ffcccc';
                        previewContainer.innerHTML = `
                            <div style="padding: 20px; color: red;">
                                <h3>Error</h3>
                                <p>${error.message}</p>
                            </div>
                        `;
                    });
            }
            
            function renderDocxPreview(docxData) {
                try {
                    statusElement.textContent = 'Rendering document...';
                    
                    docx.renderAsync(docxData, previewContainer)
                        .then(result => {
                            statusElement.textContent = 'Document rendered successfully!';
                            statusElement.style.backgroundColor = '#ccffcc';
                        })
                        .catch(error => {
                            statusElement.textContent = `Rendering error: ${error.message}`;
                            statusElement.style.backgroundColor = '#ffcccc';
                            previewContainer.innerHTML = `
                                <div style="padding: 20px; color: red;">
                                    <h3>Rendering Error</h3>
                                    <p>${error.message}</p>
                                    <pre>${error.stack || 'No stack trace available'}</pre>
                                </div>
                            `;
                        });
                } catch (error) {
                    statusElement.textContent = `Setup error: ${error.message}`;
                    statusElement.style.backgroundColor = '#ffcccc';
                    previewContainer.innerHTML = `
                        <div style="padding: 20px; color: red;">
                            <h3>Setup Error</h3>
                            <p>${error.message}</p>
                            <pre>${error.stack || 'No stack trace available'}</pre>
                        </div>
                    `;
                }
            }
        });
    </script>
</body>
</html>
