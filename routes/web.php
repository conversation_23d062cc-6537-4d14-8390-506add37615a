<?php

use App\Http\Controllers\CaseFileController;
use App\Http\Controllers\CaseFileDocumentController;
use App\Http\Controllers\CorrespondenceController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\LegalResearchItemController;
use App\Http\Controllers\OpenAiProjectController;
use App\Http\Controllers\TranscriptionController;
use App\Http\Controllers\UserLegalResearchController;
// use App\Http\Controllers\DocusealController; // Commented out for now
use App\Livewire\EnhancedApiTokenManager;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Http\Request;

// Register the Broadcast routes for Pusher authentication
Broadcast::routes(['middleware' => ['web', 'auth']]);

Route::get('/', function () {
    if (auth()->check()) {
        return redirect()->route('dashboard');
    }
    return view('welcome');
})->name('home');

Route::get('/land', function () {

    return view('welcome');
})->name('land');

// Terms and Privacy Policy routes
Route::get('/terms', function () {
    return view('terms', ['terms' => Illuminate\Support\Facades\File::get(resource_path('markdown/terms.md'))]);
});

Route::get('/privacy-policy', function () {
    return view('policy', ['policy' => Illuminate\Support\Facades\File::get(resource_path('markdown/policy.md'))]);
});


Route::get('/test', function () {
    return view('test');
})->name('testr');

Route::get('/test-exhibit', function () {
    return view('test-exhibit');
})->middleware(['auth:sanctum'])->name('test-exhibit');

Route::get('/test-caption-form', function () {
    return view('test-caption-form');
})->middleware(['auth:sanctum'])->name('test-caption-form');

// Public document sharing routes (no auth required)
Route::get('/shared/documents/{token}', [\App\Http\Controllers\DocumentShareController::class, 'show'])->name('shared.documents');
Route::get('/shared/documents/{token}/download/{document}', [\App\Http\Controllers\DocumentShareController::class, 'download'])->name('shared.documents.download');

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/dashboard', function (Request $request) {
        $search = $request->input('search');

        // For PostgreSQL compatibility, we'll use a different approach instead of UNION
        // Get all case files where user is either owner or active collaborator
        $query = \App\Models\CaseFile::where(function ($query) {
            $query->where('user_id', auth()->id()) // User is owner
                ->orWhereHas('collaborators', function ($subQuery) {
                    $subQuery->where('user_id', auth()->id())
                        ->where('status', 'active');
                }); // User is active collaborator
        })->orderBy('updated_at', 'desc');

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('case_number', 'like', "%{$search}%")
                    ->orWhere('desired_outcome', 'like', "%{$search}%");
            });
        }

        // Get pending invoices to pay
        $pendingInvoices = auth()->user()->receivedInvoices()
            ->whereIn('status', ['sent', 'partial', 'overdue'])
            ->latest()
            ->take(3)
            ->get();

        // Get sent invoices
        $sentInvoices = auth()->user()->sentInvoices()
            ->latest()
            ->take(3)
            ->get();

        // Get Connect account status
        $connectAccount = auth()->user()->connectAccount;
        $canCreateInvoices = false;
        $connectStatus = 'not_setup';

        if ($connectAccount) {
            if ($connectAccount->charges_enabled && $connectAccount->details_submitted) {
                $canCreateInvoices = true;
                $connectStatus = 'active';
            } elseif ($connectAccount->details_submitted) {
                $connectStatus = 'pending';
            } else {
                $connectStatus = 'incomplete';
            }
        }

        return view('dashboard', [
            'caseFiles' => $query->paginate(5)->withQueryString(),
            'search' => $search,
            'pendingInvoices' => $pendingInvoices,
            'sentInvoices' => $sentInvoices,
            'canCreateInvoices' => $canCreateInvoices,
            'connectAccount' => $connectAccount,
            'connectStatus' => $connectStatus
        ]);
    })->name('dashboard');

    // Add new docket routes
    Route::get('/case-files/{caseFile}/docket', [\App\Http\Controllers\DocketController::class, 'index'])
        ->name('case-files.docket');

    Route::resource('case-files', \App\Http\Controllers\CaseFileController::class);
    // Apply credits middleware to the store route only
    Route::post('case-files', [\App\Http\Controllers\CaseFileController::class, 'store'])
        ->middleware(['credits:100'])
        ->name('case-files.store');
    // Draft routes - AI Editor is the primary view
    Route::get('case-files/{caseFile}/drafts', [\App\Http\Controllers\DraftController::class, 'index'])
        ->name('case-files.drafts.index');
    Route::get('case-files/{caseFile}/drafts/create', [\App\Http\Controllers\DraftController::class, 'create'])
        ->name('case-files.drafts.create');
    Route::post('case-files/{caseFile}/drafts', [\App\Http\Controllers\DraftController::class, 'store'])
        ->name('case-files.drafts.store');
    Route::get('case-files/{caseFile}/drafts/{draft}/details', [\App\Http\Controllers\DraftController::class, 'show'])
        ->name('case-files.drafts.details');
    Route::get('case-files/{caseFile}/drafts/{draft}/edit', [\App\Http\Controllers\DraftController::class, 'edit'])
        ->name('case-files.drafts.edit');
    Route::put('case-files/{caseFile}/drafts/{draft}', [\App\Http\Controllers\DraftController::class, 'update'])
        ->name('case-files.drafts.update');
    Route::delete('case-files/{caseFile}/drafts/{draft}', [\App\Http\Controllers\DraftController::class, 'destroy'])
        ->name('case-files.drafts.destroy');
    Route::get('case-files/{caseFile}/drafts/{draft}', \App\Livewire\Drafts\AiDocumentEditor::class)
        ->name('case-files.drafts.show');


    // Document Templates
    Route::resource('document-templates', \App\Http\Controllers\DocumentTemplateController::class);
    Route::post('document-templates/{documentTemplate}/create-draft', [\App\Http\Controllers\DocumentTemplateController::class, 'createDraft'])
        ->name('document-templates.create-draft');
});

// AUTHENTICATED USER ROUTES
Route::middleware(['auth'])->group(function () {
    // Legal Research Routes
    Route::get('/legal-research', [UserLegalResearchController::class, 'index'])->name('legal-research.index');

    Route::post('/transcribe', [TranscriptionController::class, 'transcribe'])
        ->name('transcribe');
    // ->middleware(['web', 'auth:sanctum', 'credits:0']);

    Route::resource('case-files.documents', CaseFileDocumentController::class)
        ->only(['index', 'store', 'show'])->names([
            'index' => 'case-files.documents.index',
            'store' => 'case-files.documents.store',
            'show' => 'case-files.documents.show'
        ]);

    // Document generation and download
    Route::post('drafts/{draft}/generate-document', [\App\Http\Controllers\DocumentController::class, 'generate'])
        ->name('drafts.generate-document');
    Route::get('documents/{document}/download', [\App\Http\Controllers\DocumentController::class, 'download'])
        ->name('documents.download')
        ->middleware('auth');

    // Updated correspondence route to fetch the CaseFile model
    Route::get('/case-files/{caseFile}/correspondences', function (App\Models\CaseFile $caseFile) {
        return view('case-files.correspondences.index', ['caseFile' => $caseFile]);
    })->name('case-files.correspondences.index');

    Route::get('/case-files/{caseFile}/correspondences/{thread}', [CorrespondenceController::class, 'show'])
        ->name('case-files.correspondences.show');

    Route::get('/invitations', function () {
        return view('invitations.index');
    })->name('invitations');

    // Legal Research Items
    Route::post('/case-files/{caseFile}/research-items/generate', [\App\Http\Controllers\LegalResearchItemController::class, 'generate'])
        ->name('case-files.research-items.generate');
    // ->middleware(['credits:0']);
    Route::get('/case-files/{caseFile}/research-items', [\App\Http\Controllers\LegalResearchItemController::class, 'index'])
        ->name('case-files.research-items.index');
    Route::put('/research-items/{item}', [\App\Http\Controllers\LegalResearchItemController::class, 'update'])
        ->name('research-items.update');

    // Standalone Research Items Page
    Route::get('/case-files/{caseFile}/research', function (\App\Models\CaseFile $caseFile) {
        return view('case-files.research', ['caseFile' => $caseFile]);
    })->name('case-files.research');

    // Case Strategy Routes
    Route::get('/case-files/{caseFile}/strategies', [\App\Http\Controllers\StrategyController::class, 'index'])
        ->name('case-files.strategies.index');
    Route::post('/case-files/{caseFile}/strategies', [\App\Http\Controllers\StrategyController::class, 'store'])
        ->name('case-files.strategies.store');
    Route::get('/case-files/{caseFile}/strategy', [\App\Http\Controllers\StrategyController::class, 'create'])
        ->name('case-files.strategy.create');
    Route::get('/case-files/{caseFile}/strategy/{strategy}', [\App\Http\Controllers\StrategyController::class, 'edit'])
        ->name('case-files.strategy.edit');
    Route::delete('/case-files/{caseFile}/strategies/{strategy}', [\App\Http\Controllers\StrategyController::class, 'destroy'])
        ->name('case-files.strategies.destroy');
});
// AUTHENTICATED USER ROUTES


Route::middleware(['auth', 'can:manage-project-tokens'])->group(function () {
    Route::get('/manage-project-tokens', [OpenAiProjectController::class, 'index'])->name('openai.projects.index');
    Route::resource('openai/projects', OpenAiProjectController::class)
        ->except('index')
        ->names([
            'create' => 'openai.projects.create',
            'store' => 'openai.projects.store',
            'show' => 'openai.projects.show',
            'edit' => 'openai.projects.edit',
            'update' => 'openai.projects.update',
            'destroy' => 'openai.projects.destroy'
        ]);
});

// Route moved to DocumentController

Route::get('/documents/{document}/show', [DocumentController::class, 'show'])
    ->name('document.show')
    ->middleware(['signed', 'auth']);

Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {
    Route::get('/address-book', function () {
        return view('address-book.index');
    })->name('address-book.index');
});

// Interview routes
Route::middleware(['auth'])->group(function () {
    Route::get('/interview/{caseFile}', [\App\Http\Controllers\InterviewController::class, 'show'])
        ->name('interview.show');
});

// User search and private chat routes
Route::middleware(['auth'])->group(function () {
    // User search
    Route::get('/users/search', function () {
        return view('users.search');
    })->name('users.search');

    // Private chat
    Route::get('/messages', function () {
        return view('private-chat.index');
    })->name('private-chat.index');

    Route::get('/messages/{thread}', function (\App\Models\PrivateThread $thread) {
        // Check if user is a participant
        if (!$thread->participants()->where('users.id', \Illuminate\Support\Facades\Auth::id())->exists()) {
            abort(403, 'You are not a participant in this conversation.');
        }

        return view('private-chat.show', ['thread' => $thread]);
    })->name('private-chat.show');
});

// Subscription and credit routes
Route::middleware(['auth'])->group(function () {
    // Subscription routes
    Route::get('/subscriptions', [\App\Http\Controllers\SubscriptionController::class, 'index'])->name('subscriptions.index');
    Route::post('/subscriptions', [\App\Http\Controllers\SubscriptionController::class, 'store'])->name('subscriptions.store');
    Route::get('/subscriptions/manage', [\App\Http\Controllers\SubscriptionController::class, 'show'])->name('subscriptions.show');
    Route::delete('/subscriptions', [\App\Http\Controllers\SubscriptionController::class, 'destroy'])->name('subscriptions.destroy');
    Route::post('/subscriptions/resume', [\App\Http\Controllers\SubscriptionController::class, 'resume'])->name('subscriptions.resume');
    Route::put('/subscriptions/payment-method', [\App\Http\Controllers\SubscriptionController::class, 'updatePaymentMethod'])->name('subscriptions.payment-method.update');

    // Credit routes
    Route::get('/credits', [\App\Http\Controllers\CreditController::class, 'index'])->name('credits.index');
    Route::get('/credits/purchase', [\App\Http\Controllers\CreditController::class, 'showPurchaseForm'])->name('credits.purchase.form');
    Route::post('/credits/purchase', [\App\Http\Controllers\CreditController::class, 'purchase'])->name('credits.purchase');
    Route::get('/credits/purchase/complete', [\App\Http\Controllers\CreditController::class, 'completePayment'])->name('credits.purchase.complete');
    Route::get('/credits/transactions', [\App\Http\Controllers\CreditController::class, 'transactions'])->name('credits.transactions');

    // Document sharing routes (authenticated)
    Route::post('/documents/share', [\App\Http\Controllers\DocumentShareController::class, 'store'])->name('documents.share');

    // Connect account routes
    Route::get('/connect', [\App\Http\Controllers\ConnectController::class, 'index'])->name('connect.index');
    Route::post('/connect/onboarding', [\App\Http\Controllers\ConnectController::class, 'startOnboarding'])->name('connect.onboarding');
    Route::get('/connect/complete', [\App\Http\Controllers\ConnectController::class, 'completeOnboarding'])->name('connect.complete');
    Route::get('/connect/refresh/{user}', [\App\Http\Controllers\ConnectController::class, 'refreshOnboarding'])->name('connect.refresh');
    Route::get('/connect/dashboard', [\App\Http\Controllers\ConnectController::class, 'viewDashboard'])->name('connect.dashboard');

    // Invoice routes
    Route::get('/invoices', [\App\Http\Controllers\InvoiceController::class, 'index'])->name('invoices.index');
    Route::get('/invoices/received', [\App\Http\Controllers\InvoiceController::class, 'received'])->name('invoices.received');
    Route::get('/invoices/create', [\App\Http\Controllers\InvoiceController::class, 'create'])->name('invoices.create');
    Route::post('/invoices', [\App\Http\Controllers\InvoiceController::class, 'store'])->name('invoices.store');
    Route::get('/invoices/{invoice}', [\App\Http\Controllers\InvoiceController::class, 'show'])->name('invoices.show');
    Route::put('/invoices/{invoice}/status', [\App\Http\Controllers\InvoiceController::class, 'updateStatus'])->name('invoices.update-status');
    Route::get('/invoices/{invoice}/pay', [\App\Http\Controllers\InvoiceController::class, 'showPaymentForm'])->name('invoices.pay');
    Route::post('/invoices/{invoice}/pay', [\App\Http\Controllers\InvoiceController::class, 'processPayment'])->name('invoices.process-payment');
});

// Stripe webhook
Route::post('/stripe/webhook', [\App\Http\Controllers\StripeWebhookController::class, 'handleWebhook'])->name('cashier.webhook');

// Video Meeting Routes
Route::middleware(['auth'])->group(function () {
    Route::post('/video-meetings/create', [\App\Http\Controllers\VideoMeetingController::class, 'createMeeting'])->name('video-meetings.create');
    Route::get('/video-meetings/join/{roomName?}', [\App\Http\Controllers\VideoMeetingController::class, 'joinMeeting'])->name('video-meetings.join');
    Route::get('/video-meetings/embed/{roomName}', [\App\Http\Controllers\VideoMeetingController::class, 'embedMeeting'])->name('video-meetings.embed');
});

// Docuseal routes - Commented out for now
/*
Route::middleware(['auth'])->prefix('docuseal')->name('docuseal.')->group(function () {
    Route::get('/templates', [DocusealController::class, 'listTemplates'])->name('templates');
    Route::get('/create', [DocusealController::class, 'showCreateForm'])->name('create');
    Route::post('/create', [DocusealController::class, 'createDocumentForSigning'])->name('store');
    Route::get('/sign', [DocusealController::class, 'showSigningForm'])->name('sign');
    Route::get('/documents/{externalId}', [DocusealController::class, 'viewCompletedDocuments'])->name('documents');
});

// Docuseal webhook (no auth required)
Route::post('/docuseal/webhook', [DocusealController::class, 'handleWebhook'])->name('docuseal.webhook');
*/
