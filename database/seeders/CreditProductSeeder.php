<?php

namespace Database\Seeders;

use App\Models\CreditProduct;
use Illuminate\Database\Seeder;

class CreditProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = [
            [
                'name' => 'Small Credit Pack',
                'description' => '500 Credits',
                'credits' => 500,
                'price_cents' => 700, // $7.00
                'is_active' => true,
            ],
            [
                'name' => 'Medium Credit Pack',
                'description' => '1,200 Credits',
                'credits' => 1200,
                'price_cents' => 1500, // $15.00
                'is_active' => true,
            ],
            [
                'name' => 'Large Credit Pack',
                'description' => '2,000 Credits',
                'credits' => 2500,
                'price_cents' => 2800, // $28.00
                'is_active' => true,
            ],
        ];

        foreach ($products as $product) {
            CreditProduct::updateOrCreate(
                ['name' => $product['name']],
                $product
            );
        }
    }
}
