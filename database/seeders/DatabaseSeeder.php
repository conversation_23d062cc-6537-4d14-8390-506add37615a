<?php

namespace Database\Seeders;

use App\Models\User;
use App\Services\CreditService;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $password = Hash::make('beasty09');

        // Lawrenceville, Georgia coordinates
        $lawrencevilleLatitude = 33.9562;
        $lawrencevilleLongitude = -83.9879;

        // Create a test user if one doesn't already exist
        if (!User::where('email', '<EMAIL>')->exists() && !User::where('email', '<EMAIL>')->exists()) {
            $ianUser = User::create([
                'name' => '<PERSON> Bruce',
                'email' => '<EMAIL>',
                'username' => 'ianbrucey',
                'password' => $password,
                'latitude' => $lawrencevilleLatitude,
                'longitude' => $lawrencevilleLongitude,
                'zip_code' => '30046',
            ]);

//            User::create([
//                'name' => 'Test User',
//                'email' => '<EMAIL>',
//                'username' => 'testuser',
//                'password' => $password,
//                'latitude' => $lawrencevilleLatitude,
//                'longitude' => $lawrencevilleLongitude,
//                'zip_code' => '30046',
//            ]);

//            // Create an attorney user
            $attorneyUser = User::create([
                'name' => 'Attorney User',
                'email' => '<EMAIL>',
                'username' => 'attorneyuser',
                'password' => $password,
                'is_attorney' => true,
                'bar_card_number' => '123456GA',
                'latitude' => $lawrencevilleLatitude,
                'longitude' => $lawrencevilleLongitude,
                'zip_code' => '30046',
            ]);

            // Add 30,000 credits to both users
            $creditService = new CreditService();
            $creditService->addCredits($ianUser, 30000, 'Initial seed credits');
            $creditService->addCredits($attorneyUser, 30000, 'Initial seed credits');
        }

        $this->call([
            OpenAiProjectSeeder::class,
            CreditProductSeeder::class
        ]);
    }
}
