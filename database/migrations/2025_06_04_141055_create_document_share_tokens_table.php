<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('document_share_tokens', function (Blueprint $table) {
            $table->id();
            $table->uuid('token')->unique();
            $table->json('document_ids'); // Array of document IDs being shared
            $table->string('recipient_email');
            $table->text('message')->nullable(); // Optional message from sender
            $table->timestamp('expires_at');
            $table->integer('access_count')->default(0);
            $table->integer('max_access_count')->default(10); // Configurable access limit
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_accessed_at')->nullable();
            $table->timestamps();

            // Indexes for performance
            $table->index(['token', 'is_active']);
            $table->index(['expires_at', 'is_active']);
            $table->index('created_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('document_share_tokens');
    }
};
