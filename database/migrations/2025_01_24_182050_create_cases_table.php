<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('case_files', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();
            $table->string('title');
            $table->string('case_number')->nullable();
            $table->text('desired_outcome');
            $table->string('status')->default('open');
            $table->date('filed_date')->nullable();
            $table->json('case_types')->nullable();
            $table->date('date_of_incident')->nullable();
            $table->text('initial_summary')->nullable();
            $table->string('interview_status')->default('not_started');
            $table->foreignId('openai_project_id')->nullable(); // Removed constraint
            $table->string('openai_assistant_id')->nullable();
            $table->string('openai_vector_store_id')->nullable();
            $table->boolean('collaboration_enabled')->default(false);
            $table->integer('max_collaborators')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('case_files');
    }
};
