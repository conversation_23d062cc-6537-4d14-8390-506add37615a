<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->string('commitment_type')->nullable()->after('stripe_price');
            $table->timestamp('commitment_start')->nullable()->after('commitment_type');
            $table->timestamp('commitment_end')->nullable()->after('commitment_start');
            $table->string('upfront_payment_intent_id')->nullable()->after('commitment_end');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn(['commitment_type', 'commitment_start', 'commitment_end', 'upfront_payment_intent_id']);
        });
    }
};
