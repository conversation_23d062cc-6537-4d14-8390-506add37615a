<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('drafts', function (Blueprint $table) {
            $table->unsignedBigInteger('template_id')->nullable()->after('case_file_id');
            $table->unsignedInteger('version')->default(1)->after('status');
            $table->foreignId('last_edited_by')->nullable()->after('version');
            $table->foreignId('document_id')->nullable()->after('last_edited_by');
            $table->json('sections_structure')->nullable()->after('structured_context');
            $table->json('ai_generation_prompts')->nullable()->after('sections_structure');
            $table->json('metadata')->nullable()->after('ai_generation_prompts');

            // Add foreign key constraints
            $table->foreign('last_edited_by')->references('id')->on('users')->nullOnDelete();
            $table->foreign('document_id')->references('id')->on('documents')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('drafts', function (Blueprint $table) {
            $table->dropForeign(['last_edited_by']);
            $table->dropForeign(['document_id']);


            $table->dropColumn([
                'template_id',
                'version',
                'last_edited_by',
                'document_id',
                'sections_structure',
                'ai_generation_prompts',
                'metadata'
            ]);
        });
    }
};
