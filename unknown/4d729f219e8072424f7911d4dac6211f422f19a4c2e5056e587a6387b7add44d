<div x-data>
    <div class="mb-8 text-center">
        <img src="https://justicequest.us-mia-1.linodeobjects.com/public/images/en/JQ.png" class="h-20 mx-auto mb-4 animate-pulse-subtle" alt="Justice Quest Logo">
        <h2 class="text-2xl font-bold text-white">Login to Your Account</h2>
    </div>

    <form wire:submit="login" class="space-y-6">
        <div>
            <label for="email" class="block text-sm font-medium text-white">Email</label>
            <input id="email" wire:model="email" type="email" required class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-red-500/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
            @error('email')
                <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
            @enderror
        </div>

        <div>
            <label for="password" class="block text-sm font-medium text-white">Password</label>
            <input id="password" wire:model="password" type="password" required class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-red-500/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
            @error('password')
                <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
            @enderror
        </div>

        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <input id="remember" wire:model="remember" type="checkbox" class="w-4 h-4 text-red-600 bg-white border-gray-400 rounded focus:ring-red-500">
                <label for="remember" class="block ml-2 text-sm text-gray-300">Remember me</label>
            </div>

            @if (Route::has('password.request'))
                <a href="{{ route('password.request') }}" class="text-sm text-red-400 hover:text-red-300">
                    Forgot password?
                </a>
            @endif
        </div>

        <div>
            <button type="submit" class="flex justify-center w-full px-4 py-2 text-sm font-medium text-white transition-colors duration-300 bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" wire:loading.attr="disabled">
                <span wire:loading.remove>Sign in</span>
                <span wire:loading>Processing...</span>
            </button>
        </div>
    </form>

    <div class="mt-6 text-center">
        <p class="text-sm text-gray-400">Don't have an account?</p>
        <button @click="showLoginModal = false; showRegisterModal = true" class="mt-2 text-sm text-red-400 hover:text-red-300">
            Create an account
        </button>
    </div>
</div>
