<x-app-layout>
    <div class="flex items-center justify-center min-h-screen">
        <div class="max-w-md p-8 text-center card bg-base-200">
            <div class="card-body">
                <div class="flex justify-center mb-6">
                    <div class="p-4 rounded-full bg-warning/10">
                        <svg class="w-16 h-16 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                </div>
                <h2 class="mb-4 text-3xl font-bold">{{ __('Error') }} {{ $exception->getStatusCode() }}</h2>
                <p class="mb-6 text-base-content/70">
                    {{ $exception->getMessage() ?: __('An error occurred while processing your request.') }}
                </p>
                <div class="justify-center space-x-4 card-actions">
                    <a href="{{ route('dashboard') }}" class="btn btn-primary">
                        {{ __('Go to Dashboard') }}
                    </a>
                    <a href="{{ url()->previous() }}" class="btn btn-outline">
                        {{ __('Go Back') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>