<?php

namespace App\Services\LegalResearch\Services;

use App\Services\LegalResearch\Contracts\ResearchServiceInterface;

class StageHandResearchService implements ResearchServiceInterface
{
    public function research(string $query, array $parameters, int $caseFileId): array
    {
        // Implementation coming in next phase
        return [];
    }

    public function canHandle(string $query, array $parameters): bool
    {
        return isset($parameters['known_sources'])
            && isset($parameters['single_topic'])
            && $parameters['complexity'] < 0.7;
    }

    public function getExpectedResponseTime(): int
    {
        return 30; // 30 seconds
    }
}