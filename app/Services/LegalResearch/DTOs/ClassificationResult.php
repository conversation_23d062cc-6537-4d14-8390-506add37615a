<?php

namespace App\Services\LegalResearch\DTOs;

use App\Services\LegalResearch\Enums\ResearchTier;
use App\Services\LegalResearch\Enums\ConfidenceLevel;

class ClassificationResult
{
    public const TIER_DIRECT = 'direct';
    public const TIER_COURT_LISTENER = 'court_listener';
    public const TIER_STAGE_HAND = 'stage_hand';
    public const TIER_GPT_RESEARCHER = 'gpt_researcher';

    public function __construct(
        public readonly ResearchTier $tier,
        public readonly ConfidenceLevel $confidence,
        public readonly int $expectedTime,
        public readonly string $reasoning
    ) {}

    public function isDirectResponse(): bool
    {
        return $this->tier === self::TIER_DIRECT;
    }

    public function isCourtListener(): bool
    {
        return $this->tier === self::TIER_COURT_LISTENER;
    }

    public function isStageHand(): bool
    {
        return $this->tier === self::TIER_STAGE_HAND;
    }

    public function isGptResearcher(): bool
    {
        return $this->tier === self::TIER_GPT_RESEARCHER;
    }

    public function getExpectedResponseTime(): int
    {
        return match($this->tier) {
            self::TIER_DIRECT => 1,        // 1 second
            self::TIER_COURT_LISTENER => 5, // 5 seconds
            self::TIER_STAGE_HAND => 30,    // 30 seconds
            self::TIER_GPT_RESEARCHER => 300 // 5 minutes
        };
    }
}
