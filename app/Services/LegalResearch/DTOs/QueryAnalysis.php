<?php

namespace App\Services\LegalResearch\DTOs;

use App\Services\LegalResearch\Enums\ConfidenceLevel;

class QueryAnalysis
{
    public function __construct(
        public readonly ConfidenceLevel $confidence,
        public readonly array $requiredContextTypes,
        public readonly array $legalConcepts,
        public readonly string $timeSensitivity,
        public readonly array $jurisdictions,
        public readonly string $complexity
    ) {}

    public function requiresStrategyFormation(): bool
    {
        return in_array('strategy', $this->requiredContextTypes) ||
               $this->complexity === 'high';
    }
}
