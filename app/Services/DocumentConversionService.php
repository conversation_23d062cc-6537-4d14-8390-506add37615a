<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Config;

class DocumentConversionService
{
    /**
     * Convert a DOCX file to PDF
     *
     * @param string $docxPath Path to the DOCX file
     * @return string|false Path to the PDF file or false if conversion failed
     */
    public function convertDocxToPdf(string $docxPath)
    {
        $pdfPath = pathinfo($docxPath, PATHINFO_DIRNAME) . '/' .
                  pathinfo($docxPath, PATHINFO_FILENAME) . '.pdf';
        
        // Try LibreOffice conversion first if enabled
        if (Config::get('services.document_conversion.use_libreoffice', true)) {
            $result = $this->convertWithLibreOffice($docxPath, $pdfPath);
            if ($result) {
                return $pdfPath;
            }
        }
        
        // Fall back to cloud API if enabled
        if (Config::get('services.document_conversion.use_cloud_api', true)) {
            $result = $this->convertWithCloudApi($docxPath, $pdfPath);
            if ($result) {
                return $pdfPath;
            }
        }
        
        // If all conversion methods failed
        Log::error('All document conversion methods failed', [
            'document_path' => $docxPath
        ]);
        
        return false;
    }
    
    /**
     * Convert DOCX to PDF using LibreOffice
     *
     * @param string $docxPath Path to the DOCX file
     * @param string $pdfPath Expected output PDF path
     * @return bool Whether conversion was successful
     */
    protected function convertWithLibreOffice(string $docxPath, string $pdfPath)
    {
        // Get LibreOffice command from config or use default
        $libreOfficeCommand = Config::get('services.document_conversion.libreoffice_command', 'soffice');
        
        // Build the conversion command
        $command = "{$libreOfficeCommand} --headless --convert-to pdf --outdir " . 
                   escapeshellarg(pathinfo($docxPath, PATHINFO_DIRNAME)) . " " . 
                   escapeshellarg($docxPath);
        
        Log::info('Attempting LibreOffice conversion', [
            'command' => $command
        ]);
        
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0 || !file_exists($pdfPath)) {
            Log::warning('LibreOffice conversion failed', [
                'command' => $command,
                'return_code' => $returnCode,
                'output' => $output
            ]);
            return false;
        }
        
        Log::info('LibreOffice conversion successful', [
            'pdf_path' => $pdfPath
        ]);
        
        return true;
    }
    
    /**
     * Convert DOCX to PDF using a cloud API
     *
     * @param string $docxPath Path to the DOCX file
     * @param string $pdfPath Expected output PDF path
     * @return bool Whether conversion was successful
     */
    protected function convertWithCloudApi(string $docxPath, string $pdfPath)
    {
        // Get API configuration
        $apiUrl = Config::get('services.document_conversion.api_url');
        $apiKey = Config::get('services.document_conversion.api_key');
        
        if (empty($apiUrl) || empty($apiKey)) {
            Log::warning('Cloud API conversion not configured', [
                'api_url_set' => !empty($apiUrl),
                'api_key_set' => !empty($apiKey)
            ]);
            return false;
        }
        
        try {
            Log::info('Attempting cloud API conversion', [
                'document_path' => $docxPath
            ]);
            
            // Read the DOCX file
            $fileContent = file_get_contents($docxPath);
            if ($fileContent === false) {
                Log::error('Failed to read DOCX file', [
                    'document_path' => $docxPath
                ]);
                return false;
            }
            
            // Convert using API (example using ConvertAPI)
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $apiKey
            ])->attach(
                'file', $fileContent, basename($docxPath)
            )->post($apiUrl);
            
            if ($response->failed()) {
                Log::error('Cloud API conversion failed', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return false;
            }
            
            // Save the PDF response to the expected path
            $pdfContent = $response->body();
            file_put_contents($pdfPath, $pdfContent);
            
            if (!file_exists($pdfPath)) {
                Log::error('Failed to save converted PDF', [
                    'pdf_path' => $pdfPath
                ]);
                return false;
            }
            
            Log::info('Cloud API conversion successful', [
                'pdf_path' => $pdfPath
            ]);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Exception during cloud API conversion', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
}
