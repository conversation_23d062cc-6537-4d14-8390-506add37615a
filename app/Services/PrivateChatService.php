<?php

namespace App\Services;

use App\Events\NewPrivateMessage;
use App\Models\PrivateMessage;
use App\Models\PrivateThread;
use App\Models\User;
use App\Services\Translation\GeminiTranslationService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Broadcast;

class PrivateChatService
{
    protected $translationService;

    public function __construct(GeminiTranslationService $translationService)
    {
        $this->translationService = $translationService;
    }

    /**
     * Find or create a private thread between users.
     *
     * @param User $currentUser The current user
     * @param User $otherUser The user to chat with
     * @return PrivateThread The thread
     */
    public function findOrCreateThread(User $currentUser, User $otherUser): PrivateThread
    {
        // Check if a thread already exists between these users
        $existingThread = $currentUser->privateThreads()
            ->whereHas('participants', function ($query) use ($otherUser) {
                $query->where('users.id', $otherUser->id);
            })
            ->first();

        if ($existingThread) {
            return $existingThread;
        }

        // Create a new thread
        $thread = PrivateThread::create([
            'last_activity_at' => now(),
        ]);

        // Add participants
        $thread->participants()->attach([
            $currentUser->id => ['last_read_at' => now()],
            $otherUser->id => ['last_read_at' => null],
        ]);

        return $thread;
    }

    /**
     * Send a message in a private thread.
     *
     * @param PrivateThread $thread The thread
     * @param User $sender The user sending the message
     * @param string $content The message content
     * @return PrivateMessage The created message
     */
    public function sendMessage(PrivateThread $thread, User $sender, string $content): PrivateMessage
    {
        // Create the message
        $message = PrivateMessage::create([
            'private_thread_id' => $thread->id,
            'user_id' => $sender->id,
            'original_content' => $content,
            'original_language' => $sender->language ?? 'en',
        ]);

        // Update thread activity
        $thread->update(['last_activity_at' => now()]);

        // Update sender's last_read_at
        $thread->participants()->updateExistingPivot($sender->id, ['last_read_at' => now()]);

        // Get other participants
        $otherParticipants = $thread->participants()
            ->where('users.id', '!=', $sender->id)
            ->get();

        // Translate message for each participant with a different language
        foreach ($otherParticipants as $participant) {
            if (($participant->language ?? 'en') !== ($sender->language ?? 'en')) {
                try {
                    $translatedContent = $this->translationService->translate(
                        $content,
                        $participant->language ?? 'en',
                        $sender->language ?? 'en'
                    );

                    // Only update if translation is different from original
                    if ($translatedContent !== $content) {
                        $message->update(['translated_content' => $translatedContent]);
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to translate message', [
                        'message_id' => $message->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        // Broadcast the message event
        try {
            // Broadcast the message directly
            broadcast(new NewPrivateMessage($message));
            Log::info('Message broadcast successfully', [
                'message_id' => $message->id,
                'thread_id' => $thread->id,
                'user_id' => $sender->id,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to broadcast message', [
                'message_id' => $message->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        return $message;
    }

    /**
     * Mark a thread as read for a user.
     *
     * @param PrivateThread $thread The thread
     * @param User $user The user
     * @return void
     */
    public function markAsRead(PrivateThread $thread, User $user): void
    {
        $thread->participants()->updateExistingPivot($user->id, ['last_read_at' => now()]);
    }

    /**
     * Get unread threads count for a user.
     *
     * @param User $user The user
     * @return int The count of unread threads
     */
    public function getUnreadThreadsCount(User $user): int
    {
        return $user->privateThreads()
            ->whereRaw('private_thread_participants.last_read_at IS NULL OR private_threads.last_activity_at > private_thread_participants.last_read_at')
            ->count();
    }

    /**
     * Get threads for a user.
     *
     * @param User $user The user
     * @return \Illuminate\Database\Eloquent\Collection The threads
     */
    public function getThreadsForUser(User $user)
    {
        return $user->privateThreads()
            ->with(['participants' => function ($query) use ($user) {
                $query->where('users.id', '!=', $user->id);
            }])
            ->orderBy('last_activity_at', 'desc')
            ->get();
    }

    /**
     * Get messages for a thread.
     *
     * @param PrivateThread $thread The thread
     * @param int $limit The number of messages to get
     * @param int $page The page number
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator The messages
     */
    public function getMessagesForThread(PrivateThread $thread, int $limit = 15, int $page = 1)
    {
        return $thread->messages()
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->paginate($limit, ['*'], 'page', $page);
    }
}
