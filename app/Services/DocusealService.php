<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DocusealService
{
    protected $apiKey;
    protected $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.docuseal.api_key');
        $this->baseUrl = config('services.docuseal.base_url', 'https://api.docuseal.com');
    }

    /**
     * Create a submission for document signing
     *
     * @param int $templateId
     * @param array $submitters
     * @param string $order
     * @param array|null $message
     * @param bool $sendEmail
     * @return array
     */
    public function createSubmission($templateId, $submitters, $order = 'random', $message = null, $sendEmail = true)
    {
        $payload = [
            'template_id' => $templateId,
            'submitters' => $submitters,
            'order' => $order,
            'send_email' => $sendEmail,
        ];

        if ($message) {
            $payload['message'] = $message;
        }

        $response = Http::withHeaders([
            'X-Auth-Token' => $this->apiKey,
            'Content-Type' => 'application/json',
        ])->post("{$this->baseUrl}/submissions", $payload);

        if ($response->failed()) {
            Log::error('Docuseal API error', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
            throw new \Exception('Failed to create document submission: ' . $response->body());
        }

        return $response->json();
    }

    /**
     * Get a submitter by external ID
     *
     * @param string $externalId
     * @return array
     */
    public function getSubmitterByExternalId($externalId)
    {
        $response = Http::withHeaders([
            'X-Auth-Token' => $this->apiKey,
            'Content-Type' => 'application/json',
        ])->get("{$this->baseUrl}/submitters", [
            'external_id' => $externalId,
        ]);

        if ($response->failed()) {
            Log::error('Docuseal API error', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
            throw new \Exception('Failed to get submitter: ' . $response->body());
        }

        return $response->json();
    }

    /**
     * Create a template from a PDF with field tags
     *
     * @param string $name
     * @param string $filePath
     * @return array
     */
    public function createTemplateFromPdf($name, $filePath)
    {
        $fileData = base64_encode(file_get_contents($filePath));

        $response = Http::withHeaders([
            'X-Auth-Token' => $this->apiKey,
            'Content-Type' => 'application/json',
        ])->post("{$this->baseUrl}/templates/pdf", [
            'name' => $name,
            'file' => $fileData,
        ]);

        if ($response->failed()) {
            Log::error('Docuseal API error', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
            throw new \Exception('Failed to create template: ' . $response->body());
        }

        return $response->json();
    }

    /**
     * Get all templates
     *
     * @return array
     */
    public function getTemplates()
    {
        $response = Http::withHeaders([
            'X-Auth-Token' => $this->apiKey,
            'Content-Type' => 'application/json',
        ])->get("{$this->baseUrl}/templates");

        if ($response->failed()) {
            Log::error('Docuseal API error', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
            throw new \Exception('Failed to get templates: ' . $response->body());
        }

        $templates = $response->json();

        // Log the raw response for debugging
        Log::info('Docuseal templates response', [
            'raw_response' => $response->body(),
            'parsed_templates' => $templates
        ]);

        return $templates;
    }

    /**
     * Update a submitter to mark as completed and provide field values
     *
     * @param int $submitterId
     * @param array $fields
     * @return array
     */
    public function completeSubmission($submitterId, $fields)
    {
        $response = Http::withHeaders([
            'X-Auth-Token' => $this->apiKey,
            'Content-Type' => 'application/json',
        ])->put("{$this->baseUrl}/submitters/{$submitterId}", [
            'completed' => true,
            'fields' => $fields,
        ]);

        if ($response->failed()) {
            Log::error('Docuseal API error', [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
            throw new \Exception('Failed to complete submission: ' . $response->body());
        }

        return $response->json();
    }
}
