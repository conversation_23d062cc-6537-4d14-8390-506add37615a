<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\Finder\Finder;

class UploadFolderToS3 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'upload:folder-to-s3 
                            {source : The source folder path to upload}
                            {destination=public/images/ : The destination path in S3 (default: public/images/)}
                            {--disk=s3 : The disk to upload to (default: s3)}
                            {--recursive : Upload files recursively from subdirectories}
                            {--force : Overwrite existing files without confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Upload a folder and its contents to S3 storage';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $sourcePath = $this->argument('source');
        $destinationPath = $this->argument('destination');
        $disk = $this->option('disk');
        $recursive = $this->option('recursive');
        $force = $this->option('force');

        // Ensure source path exists
        if (!file_exists($sourcePath)) {
            $this->error("Source folder does not exist: {$sourcePath}");
            return 1;
        }

        // Ensure source path is a directory
        if (!is_dir($sourcePath)) {
            $this->error("Source path is not a directory: {$sourcePath}");
            return 1;
        }

        // Normalize paths
        $sourcePath = rtrim($sourcePath, '/');
        $destinationPath = rtrim($destinationPath, '/');

        // Confirm upload
        if (!$force && !$this->confirm("Upload all files from '{$sourcePath}' to '{$destinationPath}' on {$disk} disk?")) {
            $this->info('Upload cancelled.');
            return 0;
        }

        // Create a finder instance to locate files
        $finder = new Finder();
        $finder->files()->in($sourcePath);
        
        // Apply recursive option
        if (!$recursive) {
            $finder->depth('== 0');
        }

        // Count total files for progress bar
        $totalFiles = count($finder);
        
        if ($totalFiles === 0) {
            $this->info("No files found in {$sourcePath}");
            return 0;
        }

        $this->info("Found {$totalFiles} files to upload.");
        $progressBar = $this->output->createProgressBar($totalFiles);
        $progressBar->start();

        $uploadedCount = 0;
        $failedCount = 0;
        $skippedCount = 0;

        foreach ($finder as $file) {
            // Get the relative path from the source directory
            $relativePath = str_replace($sourcePath, '', $file->getPathname());
            $relativePath = ltrim($relativePath, '/');
            
            // Construct the destination path in S3
            $s3Path = $destinationPath . '/' . $relativePath;
            
            try {
                // Check if file already exists
                if (!$force && Storage::disk($disk)->exists($s3Path)) {
                    $this->newLine();
                    $this->line("<fg=yellow>Skipping (already exists): {$s3Path}</>");
                    $skippedCount++;
                    $progressBar->advance();
                    continue;
                }
                
                // Upload the file to S3
                $uploaded = Storage::disk($disk)->putFileAs(
                    dirname($s3Path),
                    $file->getPathname(),
                    basename($s3Path),
                    'public'
                );
                
                if ($uploaded) {
                    $uploadedCount++;
                } else {
                    $this->newLine();
                    $this->line("<fg=red>Failed to upload: {$s3Path}</>");
                    $failedCount++;
                }
            } catch (\Exception $e) {
                $this->newLine();
                $this->line("<fg=red>Error uploading {$s3Path}: {$e->getMessage()}</>");
                $failedCount++;
            }
            
            $progressBar->advance();
        }
        
        $progressBar->finish();
        $this->newLine(2);
        
        // Display summary
        $this->info("Upload completed:");
        $this->line("- <fg=green>{$uploadedCount} files uploaded successfully</>");
        
        if ($skippedCount > 0) {
            $this->line("- <fg=yellow>{$skippedCount} files skipped (already exist)</>");
        }
        
        if ($failedCount > 0) {
            $this->line("- <fg=red>{$failedCount} files failed to upload</>");
        }
        
        return $failedCount > 0 ? 1 : 0;
    }
}
