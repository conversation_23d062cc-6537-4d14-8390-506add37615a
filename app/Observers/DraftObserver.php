<?php

namespace App\Observers;

use App\Models\Draft;
use App\Models\AssistantThread;
use Illuminate\Support\Facades\Log;

class DraftObserver
{
    /**
     * Handle the Draft "deleted" event.
     */
    public function deleted(Draft $draft): void
    {
        try {
            // Find assistant threads associated with this draft via metadata
            $threads = AssistantThread::where('case_file_id', $draft->case_file_id)
                ->where('metadata->draft_id', $draft->id)
                ->get();

            if ($threads->count() > 0) {
                foreach ($threads as $thread) {
                    // Log the deletion
                    Log::info('Deleting assistant thread associated with deleted draft', [
                        'draft_id' => $draft->id,
                        'thread_id' => $thread->id,
                        'thread_title' => $thread->title
                    ]);

                    // Delete the thread (messages will be deleted via cascade)
                    $thread->delete();
                }
                
                Log::info('Successfully deleted assistant threads for draft', [
                    'draft_id' => $draft->id,
                    'thread_count' => $threads->count()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to delete assistant threads for draft', [
                'draft_id' => $draft->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // We don't rethrow the exception since the draft is already deleted
            // and we don't want to prevent the deletion from completing
        }
    }

    /**
     * Handle the Draft "restored" event.
     */
    public function restored(Draft $draft): void
    {
        // If you implement soft deletes and restoration in the future
    }

    /**
     * Handle the Draft "force deleted" event.
     */
    public function forceDeleted(Draft $draft): void
    {
        // This will be triggered for permanent deletion if using soft deletes
        $this->deleted($draft);
    }
}
