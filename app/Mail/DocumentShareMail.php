<?php

namespace App\Mail;

use App\Models\DocumentShareToken;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Address;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class DocumentShareMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public DocumentShareToken $shareToken;
    public User $sender;
    public $documents;

    /**
     * Create a new message instance.
     */
    public function __construct(DocumentShareToken $shareToken)
    {
        $this->shareToken = $shareToken;
        $this->sender = $shareToken->creator;
        $this->documents = $shareToken->documents()->get();
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $documentCount = count($this->shareToken->document_ids);
        $subject = $documentCount === 1
            ? "{$this->sender->name} shared a document with you"
            : "{$this->sender->name} shared {$documentCount} documents with you";

        return new Envelope(
            subject: $subject,
            replyTo: [
                new Address($this->sender->email, $this->sender->name)
            ],
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.document-share',
            with: [
                'shareToken' => $this->shareToken,
                'sender' => $this->sender,
                'documents' => $this->documents,
                'shareUrl' => route('shared.documents', ['token' => $this->shareToken->token]),
                'expiresAt' => $this->shareToken->expires_at,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
