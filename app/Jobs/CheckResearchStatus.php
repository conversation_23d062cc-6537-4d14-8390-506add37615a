<?php

namespace App\Jobs;

use App\Jobs\ProcessResearchReportJob;
use App\Models\LegalResearchItem;
use App\Services\LegalResearch\GPTResearcherClient;
use App\Services\Translation\GeminiTranslationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CheckResearchStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 60; // 1 minute timeout
    public $tries = 3;

    private $item;

    /**
     * Create a new job instance.
     */
    public function __construct(LegalResearchItem $item)
    {
        $this->item = $item;
    }

    /**
     * Execute the job.
     */
    public function handle(GPTResearcherClient $client, GeminiTranslationService $translationService): void
    {
        try {
            // Skip if no research ID
            if (!$this->item->research_id) {
                return;
            }

            // Check status
            $status = $client->checkResearchStatus($this->item->research_id);

            // Log the status response for debugging
            Log::debug('Research status response', [
                'item_id' => $this->item->id,
                'research_id' => $this->item->research_id,
                'status' => $status
            ]);

            // Normalize status to uppercase for consistency
            $statusValue = strtoupper($status['status']);

            // Update item based on status
            switch ($statusValue) {
                case 'COMPLETED':
                    // Get the markdown content
                    $markdownContent = $status['markdown_content'] ?? null;

                    // Translate the markdown content if needed
                    if ($markdownContent) {
                        // Get the user's preferred language
                        $userLanguage = $this->item->caseFile->user->language ?? 'en';

                        // Only translate if the user's language is not English
                        if ($userLanguage !== 'en') {
                            Log::info('Translating research report to user language', [
                                'item_id' => $this->item->id,
                                'user_language' => $userLanguage
                            ]);

                            try {
                                $markdownContent = $translationService->translate($markdownContent, $userLanguage);
                            } catch (\Exception $e) {
                                Log::error('Failed to translate research report', [
                                    'item_id' => $this->item->id,
                                    'error' => $e->getMessage()
                                ]);
                                // Continue with the original content if translation fails
                            }
                        }
                    }

                    $this->item->update([
                        'research_status' => 'completed',
                        'research_completed_at' => now(),
                        'research_report_url' => $status['public_report_url'] ?? null,
                        'research_markdown_content' => $markdownContent,
                        'research_local_path' => $status['local_markdown_path'] ?? null,
                        'research_error' => null
                    ]);

                    // Dispatch job to process the research report for OpenAI
                    if ($markdownContent) {
                        ProcessResearchReportJob::dispatch($this->item);
                    }

                    break;

                case 'IN_PROGRESS':
                case 'INPROGRESS':
                    $this->item->update([
                        'research_status' => 'in_progress'
                    ]);

                    // Check again in 5 minutes
                    self::dispatch($this->item)->delay(now()->addMinutes(5));
                    break;

                case 'QUEUED':
                    $this->item->update([
                        'research_status' => 'queued'
                    ]);

                    // Check again in 2 minutes
                    self::dispatch($this->item)->delay(now()->addMinutes(2));
                    break;

                case 'FAILED':
                    $this->item->update([
                        'research_status' => 'failed',
                        'research_error' => $status['error'] ?? 'Unknown error'
                    ]);
                    break;

                default:
                    Log::warning('Unknown research status: ' . $status['status'], [
                        'item_id' => $this->item->id,
                        'research_id' => $this->item->research_id
                    ]);
            }
        } catch (\Exception $e) {
            Log::error('Error checking research status: ' . $e->getMessage(), [
                'item_id' => $this->item->id,
                'research_id' => $this->item->research_id,
                'trace' => $e->getTraceAsString()
            ]);

            // Retry if this is a temporary error
            if ($this->attempts() < $this->tries) {
                // Exponential backoff: 2, 4, 8 minutes
                $delay = pow(2, $this->attempts());
                self::dispatch($this->item)->delay(now()->addMinutes($delay));
            } else {
                // Update item with error status
                $this->item->update([
                    'research_status' => 'failed',
                    'research_error' => 'Failed to check research status: ' . $e->getMessage()
                ]);
            }
        }
    }
}
