<?php

namespace App\Jobs;

use App\Events\SectionContentGenerated;
use App\Models\Draft;
use App\Services\DocumentAiService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessSectionGenerationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $draftId;
    protected $prompt;
    protected $context;
    protected $sectionId;
    protected $userId;

    /**
     * Create a new job instance.
     *
     * @param int $draftId
     * @param string $prompt
     * @param array $context
     * @param string $sectionId
     * @param int $userId
     */
    public function __construct(int $draftId, string $prompt, array $context, string $sectionId, int $userId)
    {
        $this->draftId = $draftId;
        $this->prompt = $prompt;
        $this->context = $context;
        $this->sectionId = $sectionId;
        $this->userId = $userId;

        // Set the queue for this job
        $this->onQueue('ai-processing');
    }

    /**
     * Execute the job.
     */
    public function handle(DocumentAiService $documentAiService): void
    {
        try {
            Log::info('Processing section generation job', [
                'draft_id' => $this->draftId,
                'section_id' => $this->sectionId,
                'prompt_preview' => substr($this->prompt, 0, 100),
                'user_id' => $this->userId
            ]);

            // Get the draft
            $draft = Draft::findOrFail($this->draftId);

            // Initialize the AI service for this draft
            $documentAiService->initializeForDraft($draft);

            // Send to AI service
            $response = $documentAiService->generateContent($this->prompt, $this->context);

            // Log the response for debugging
//            Log::info('AI section generation response processed in job', [
//                'has_document_content' => isset($response['document_content']) && !empty($response['document_content']),
//                'document_content_length' => isset($response['document_content']) ? strlen($response['document_content']) : 0,
//                'has_explanation' => isset($response['changes_explanation']) && !empty($response['changes_explanation']),
//            ]);

            // Prepare the chat message
            $explanation = isset($response['changes_explanation']) ?
                $response['changes_explanation'] : 'Response generated by AI assistant.';

            $chatMessage = [
                'role' => 'assistant',
                'content' => $explanation,
                'timestamp' => now()->format('g:i A')
            ];

            // Broadcast the response to the client
            event(new SectionContentGenerated(
                $this->draftId,
                $response,
                $chatMessage,
                $this->sectionId,
                $this->userId
            ));
        } catch (\Exception $e) {
            Log::error('Error processing section generation job', [
                'draft_id' => $this->draftId,
                'section_id' => $this->sectionId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Broadcast error to the client
            event(new SectionContentGenerated(
                $this->draftId,
                ['error' => $e->getMessage()],
                [
                    'role' => 'system',
                    'content' => 'An error occurred while generating section content. Please try again.',
                    'timestamp' => now()->format('g:i A')
                ],
                $this->sectionId,
                $this->userId
            ));
        }
    }
}
