<?php

namespace App\Policies;

use App\Models\Draft;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class DraftPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Draft $draft): bool
    {
        // Check if the user has access to the case file
        return $user->can('view', $draft->caseFile);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true;
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Draft $draft): bool
    {
        // Check if the user has access to the case file
        return $user->can('update', $draft->caseFile);
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Draft $draft): bool
    {
        // Check if the user has access to the case file
        return $user->can('delete', $draft->caseFile);
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Draft $draft): bool
    {
        // Check if the user has access to the case file
        return $user->can('update', $draft->caseFile);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Draft $draft): bool
    {
        // Check if the user has access to the case file
        return $user->can('delete', $draft->caseFile);
    }

    /**
     * Determine whether the user can generate a document from the draft.
     */
    public function generateDocument(User $user, Draft $draft): bool
    {
        // Check if the user has access to the case file
        return $user->can('view', $draft->caseFile);
    }
}
