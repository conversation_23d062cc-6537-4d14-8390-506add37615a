<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DocumentTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'document_type',
        'description',
        'structure',
        'default_content',
        'ai_prompts',
        'metadata',
        'is_active',
        'created_by'
    ];

    protected $casts = [
        'structure' => 'array',
        'default_content' => 'array',
        'ai_prompts' => 'array',
        'metadata' => 'array',
        'is_active' => 'boolean'
    ];

    /**
     * Get the user who created this template.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the drafts that use this template.
     */
    public function drafts(): HasMany
    {
        return $this->hasMany(Draft::class, 'template_id');
    }

    /**
     * Get available document types.
     */
    public static function getDocumentTypes(): array
    {
        return [
            'complaint' => 'Complaint',
            'motion' => 'Motion',
            'affidavit' => 'Affidavit',
            'proposed_order' => 'Proposed Order',
            'certificate_of_service' => 'Certificate of Service',
            'letter' => 'Letter',
            'contract' => 'Contract'
        ];
    }
}
