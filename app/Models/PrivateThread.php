<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class PrivateThread extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'last_activity_at',
    ];

    protected $casts = [
        'last_activity_at' => 'datetime',
    ];

    /**
     * Get the participants of the thread.
     */
    public function participants(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'private_thread_participants')
            ->withPivot('last_read_at')
            ->withTimestamps();
    }

    /**
     * Get the messages for the thread.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(PrivateMessage::class);
    }

    /**
     * Update the last activity timestamp.
     *
     * @param string|null $attribute
     * @return bool
     */
    public function touch($attribute = null)
    {
        $this->last_activity_at = now();
        return parent::touch($attribute);
    }

    /**
     * Get the title for the thread, or generate one based on participants.
     *
     * @param User $currentUser
     * @return string
     */
    public function getDisplayTitle(User $currentUser): string
    {
        if (!empty($this->title)) {
            return $this->title;
        }

        // Get other participants (excluding current user)
        $otherParticipants = $this->participants()
            ->where('users.id', '!=', $currentUser->id)
            ->get();

        if ($otherParticipants->isEmpty()) {
            return 'Chat with yourself';
        }

        if ($otherParticipants->count() === 1) {
            return 'Chat with ' . $otherParticipants->first()->name;
        }

        return 'Group chat with ' . $otherParticipants->pluck('name')->join(', ', ' and ');
    }
}
