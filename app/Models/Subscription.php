<?php

namespace App\Models;

use <PERSON><PERSON>\Cashier\Subscription as CashierSubscription;

class Subscription extends CashierSubscription
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'type',
        'stripe_id',
        'stripe_status',
        'stripe_price',
        'quantity',
        'trial_ends_at',
        'ends_at',
        'commitment_type',
        'commitment_start',
        'commitment_end',
        'upfront_payment_intent_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'trial_ends_at' => 'datetime',
        'ends_at' => 'datetime',
        'commitment_start' => 'datetime',
        'commitment_end' => 'datetime',
    ];

    /**
     * Check if this is a commitment subscription.
     *
     * @return bool
     */
    public function isCommitmentSubscription(): bool
    {
        return !is_null($this->commitment_type);
    }

    /**
     * Check if the commitment period is still active.
     *
     * @return bool
     */
    public function isInCommitmentPeriod(): bool
    {
        if (!$this->isCommitmentSubscription()) {
            return false;
        }

        return $this->commitment_end && now()->lessThan($this->commitment_end);
    }

    /**
     * Get the remaining days in the commitment period.
     *
     * @return int|null
     */
    public function getRemainingCommitmentDays(): ?int
    {
        if (!$this->isInCommitmentPeriod()) {
            return null;
        }

        return now()->diffInDays($this->commitment_end);
    }
}
