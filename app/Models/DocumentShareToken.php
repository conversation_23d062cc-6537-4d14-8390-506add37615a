<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use Carbon\Carbon;

class DocumentShareToken extends Model
{
    use HasFactory;

    protected $fillable = [
        'token',
        'document_ids',
        'recipient_email',
        'message',
        'expires_at',
        'access_count',
        'max_access_count',
        'created_by',
        'is_active',
        'last_accessed_at',
    ];

    protected $casts = [
        'document_ids' => 'array',
        'expires_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->token)) {
                $model->token = Str::uuid();
            }
        });
    }

    /**
     * Get the user who created this share token
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the documents being shared
     */
    public function documents()
    {
        return Document::whereIn('id', $this->document_ids);
    }

    /**
     * Check if the token is valid and can be accessed
     */
    public function isValid(): bool
    {
        return $this->is_active
            && $this->expires_at->isFuture()
            && $this->access_count < $this->max_access_count;
    }

    /**
     * Check if the token has expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if access limit has been reached
     */
    public function hasReachedAccessLimit(): bool
    {
        return $this->access_count >= $this->max_access_count;
    }

    /**
     * Record an access to this token
     */
    public function recordAccess(): void
    {
        $this->increment('access_count');
        $this->update(['last_accessed_at' => now()]);
    }

    /**
     * Revoke the token
     */
    public function revoke(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Create a new share token with default expiration
     */
    public static function createShare(array $documentIds, string $recipientEmail, ?string $message = null, ?int $daysToExpire = 7): self
    {
        return self::create([
            'document_ids' => $documentIds,
            'recipient_email' => $recipientEmail,
            'message' => $message,
            'expires_at' => Carbon::now()->addDays($daysToExpire),
            'created_by' => auth()->id(),
        ]);
    }

    /**
     * Scope to only valid tokens
     */
    public function scopeValid($query)
    {
        return $query->where('is_active', true)
                    ->where('expires_at', '>', now())
                    ->whereRaw('access_count < max_access_count');
    }

    /**
     * Scope to expired tokens
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }
}
