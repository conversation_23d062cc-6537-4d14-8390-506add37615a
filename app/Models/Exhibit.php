<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Exhibit extends Model
{
    use HasFactory;

    protected $fillable = [
        'case_file_id',
        'draft_id',
        'document_id',
        'label',
        'description',
        'sort_order',
        'dated'
    ];

    protected $casts = [
        'dated' => 'date',
    ];

    /**
     * Get the case file that owns the exhibit
     */
    public function caseFile()
    {
        return $this->belongsTo(CaseFile::class);
    }

    /**
     * Get the draft that owns the exhibit
     */
    public function draft()
    {
        return $this->belongsTo(Draft::class);
    }

    /**
     * Get the document associated with the exhibit
     */
    public function document()
    {
        return $this->belongsTo(Document::class);
    }
}
