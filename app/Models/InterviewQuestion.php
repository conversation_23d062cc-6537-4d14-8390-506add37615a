<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;

class InterviewQuestion extends Model
{
    use HasFactory;

    protected $fillable = [
        'case_file_id',
        'question_text',
        'expected_response_type',
        'multiple_choice_options',
        'question_order',
        'is_answered',
        'answer',
        'document_id'
    ];

    protected $casts = [
        'multiple_choice_options' => 'array',
        'is_answered' => 'boolean',
    ];

    public function caseFile(): BelongsTo
    {
        return $this->belongsTo(CaseFile::class);
    }

    public function answer(): HasOne
    {
        return $this->hasOne(InterviewAnswer::class);
    }

    /**
     * Get the document attached to this answer, if any
     */
    public function document()
    {
        return $this->belongsTo(Document::class);
    }
}
