<?php

namespace App\Livewire;

use App\Models\CaseFile;
use App\Models\Document;
use Livewire\Component;
use Livewire\WithFileUploads;
use App\Services\DocumentService;
use App\Services\CreditService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use App\Services\OpenAI\CaseAssistantService;
use OpenAI\Laravel\Facades\OpenAI;

class ExhibitUploader extends Component
{
    use WithFileUploads;

    public $files = [];
    public $queuedFiles = [];
    public $documentTitles = [];
    public $documentDescriptions = [];
    public $caseFile;
    public $isSavingAll = false;
    public $savingDocuments = [];
    public $previewDocument = null;
    public $showingPreviewModal = false;
    public $documentUrl = null;
    public $insufficientCredits = false;
    public $requiredCredits = 15;
    public $currentBalance = 0;

    protected $listeners = ['removeFile', 'fileValidationError', 'previewDocument', 'closePreviewModal'];

    public function mount($caseFile, $showDocumentList = true)
    {
        $this->caseFile = $caseFile;
        $this->showDocumentList = $showDocumentList;

        if(empty($this->caseFile)) {
            throw new \Exception(__('documents.errors.no_case_file'));
        }

        // Check user's credit balance
        $this->checkCreditBalance();
    }

    /**
     * Check if the user has enough credits to upload documents
     */
    private function checkCreditBalance()
    {
        $creditService = app(CreditService::class);
        $user = Auth::user();
        $this->currentBalance = $creditService->getBalance($user);
        $this->insufficientCredits = $this->currentBalance < $this->requiredCredits;
    }

//    public function updated() {
//        dd($this->files);
//    }

    public function updatedFiles()
    {
        // Check if user has enough credits before processing files
        $this->checkCreditBalance();

        if ($this->insufficientCredits) {
            session()->flash('error', __('You need at least ' . $this->requiredCredits . ' credits to upload documents. Your current balance is ' . $this->currentBalance . ' credits.'));
            $this->files = [];
            return;
        }

        foreach ($this->files as $file) {
            $originalName = $file->getClientOriginalName();
            $sanitizedName = str_replace(' ', '_', $originalName);
            $mimeType = $file->getMimeType();
            $fileSize = $file->getSize();

            // Check file size based on type
            $maxSize = 10 * 1024 * 1024; // 10MB default

            // Increase max size for audio/video files
            if (strpos($mimeType, 'audio/') === 0) {
                $maxSize = 25 * 1024 * 1024; // 25MB for audio
            } elseif (strpos($mimeType, 'video/') === 0) {
                $maxSize = 150 * 1024 * 1024; // 150MB for video
            }

            if ($fileSize > $maxSize) {
                // Simple session flash message - works reliably
                session()->flash('error', __('documents.errors.file_too_large', [
                    'name' => $originalName,
                    'size' => round($fileSize / (1024 * 1024), 2),
                    'max' => round($maxSize / (1024 * 1024), 2)
                ]));
                continue;
            }

            // Store file and create object structure
            $storedPath = $file->storeAs('temp', $sanitizedName);

            $fileObject = [
                'file' => $file,
                'metadata' => [
                    'name' => $originalName,
                    'size' => $fileSize,
                    'type' => $mimeType,
                    'progress' => 100,
                    'temporaryUrl' => null,
                    'isAudio' => strpos($mimeType, 'audio/') === 0,
                    'isVideo' => strpos($mimeType, 'video/') === 0
                ]
            ];

            $index = count($this->queuedFiles);
            $this->queuedFiles[$index] = $fileObject;
            $this->documentTitles[$index] = '';
            $this->documentDescriptions[$index] = '';
        }

        $this->files = [];
    }

    public function removeFile($key)
    {
        unset($this->queuedFiles[$key]);
        unset($this->documentTitles[$key]);
        unset($this->documentDescriptions[$key]);

        // Re-index arrays
        $this->queuedFiles = array_values($this->queuedFiles);
        $this->documentTitles = array_values($this->documentTitles);
        $this->documentDescriptions = array_values($this->documentDescriptions);
    }

    public function saveDocument($key)
    {
        $this->savingDocuments[$key] = true;
        $tempId = $this->queuedFiles[$key]['tempId'] ?? null;

        try {
            $fileObject = $this->queuedFiles[$key];
            $title = $this->documentTitles[$key] ?? null;
            $description = $this->documentDescriptions[$key] ?? null;

            // Store document first
            $document = app(DocumentService::class)->store(
                $this->caseFile,
                $fileObject['file'],
                $title,
                $description
            );

            // Create an exhibit record linked to this document
            $exhibit = \App\Models\Exhibit::create([
                'case_file_id' => $this->caseFile->id,
                'document_id' => $document->id,
                'label' => 'Exhibit ' . $this->getNextExhibitLabel($this->caseFile->id),
                'description' => $description,
                'sort_order' => $this->getNextSortOrder($this->caseFile->id)
            ]);

            // Extract just the label part (A, B, C, etc.) without the "Exhibit " prefix
            $labelText = str_replace('Exhibit ', '', $exhibit->label);

            $this->removeFile($key);

            // Dispatch events with the exhibit label
            $this->dispatch('document-uploaded', document: $document, exhibit: $exhibit);
            $this->dispatch('document-saved', tempId: $tempId, exhibitLabel: $labelText);
        } catch (\Exception $e) {
            Log::error('Failed to upload document: ' . $e->getMessage());
            $this->addError('upload', __('documents.errors.upload_failed', ['message' => $e->getMessage()]));
        } finally {
            unset($this->savingDocuments[$key]);
        }
    }

    public function saveAllDocuments()
    {
        $this->isSavingAll = true;

        try {
            while (!empty($this->queuedFiles)) {
                $this->saveDocument(array_key_first($this->queuedFiles));
            }
        } finally {
            $this->isSavingAll = false;
        }
    }

    public function deleteDocument($documentId)
    {
        try {
            $document = Document::findOrFail($documentId);

            // Check authorization
            $this->authorize('delete', $document);

            // Delete associated exhibits first
            try {
                \App\Models\Exhibit::where('document_id', $documentId)->delete(
                );
            } catch (\Exception $e) {
                // Log::warning('Failed to delete associated exhibits: ' . $e->getMessage());
            }

            // Get the case file and vector store ID
            $caseFile = $document->caseFile;
            $vectorStoreId = $caseFile->openai_vector_store_id;
            $openaiFileId = $document->openai_file_id;

            // Configure OpenAI credentials for this case
            $assistantService = app(CaseAssistantService::class);
            $assistantService->configureCaseCredentials($caseFile);

            // Remove from OpenAI vector store if applicable
            if ($vectorStoreId && $openaiFileId && !$document->skip_vector_store) {
                try {
                    // Delete file from vector store
                    OpenAI::vectorStores()->files()->delete(
                        vectorStoreId: $vectorStoreId,
                        fileId: $openaiFileId
                    );

                    // Also delete the file from OpenAI
                    OpenAI::files()->delete($openaiFileId);

                    Log::info('Removed document from OpenAI vector store', [
                        'document_id' => $document->id,
                        'vector_store_id' => $vectorStoreId,
                        'openai_file_id' => $openaiFileId
                    ]);
                } catch (\Exception $e) {
                    // Log but continue with deletion
                    Log::warning('Failed to remove document from OpenAI vector store', [
                        'document_id' => $document->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Delete from storage
            if ($document->storage_path) {
                Storage::disk('s3')->delete($document->storage_path);
            }

            // Delete the document
            $document->delete();

            // Dispatch event for any listeners
            $this->dispatch('document-deleted', documentId: $documentId);

        } catch (\Exception $e) {
            Log::error('Failed to delete document: ' . $e->getMessage());
            $this->addError('delete', __('documents.errors.delete_failed', ['message' => $e->getMessage()]));
        }
    }

    public function render()
    {
        return view('livewire.exhibit-uploader');
    }

    /**
     * Safely add an error message, falling back to session flash if addError is not available
     *
     * @param string $key
     * @param string $message
     * @return void
     */
    private function safeAddError($key, $message)
    {
        try {
            if (method_exists($this, 'addError')) {
                $this->addError($key, $message);
            } else {
                session()->flash('error', $message);
            }
        } catch (\Exception $e) {
            session()->flash('error', $message);
            Log::warning('Error when trying to add error message', [
                'original_error' => $message,
                'exception' => $e->getMessage()
            ]);
        }
    }

    public function fileValidationError($message)
    {
        session()->flash('error', $message);
    }

    public function previewDocument($documentId)
    {
        $this->previewDocument = Document::find($documentId);

        if ($this->previewDocument) {
            // Generate a temporary URL for the document
            $this->documentUrl = Storage::disk('s3')->temporaryUrl(
                $this->previewDocument->storage_path,
                now()->addMinutes(5)
            );
        }

        $this->showingPreviewModal = true;
    }

    public function closePreviewModal()
    {
        $this->showingPreviewModal = false;
        $this->previewDocument = null;
        $this->documentUrl = null;
    }

    /**
     * Get the next available exhibit label (A, B, C, ..., Z, AA, AB, etc.)
     */
    protected function getNextExhibitLabel($caseFileId)
    {
        $lastExhibit = \App\Models\Exhibit::where('case_file_id', $caseFileId)
            ->orderBy('created_at', 'desc')
            ->first();

        if (!$lastExhibit) {
            return 'A';
        }

        // Extract the last label from the exhibit
        $lastLabel = preg_replace('/^Exhibit\s+/', '', $lastExhibit->label);

        // Convert label to next in sequence (A->B, Z->AA, AA->AB, etc.)
        return $this->incrementExhibitLabel($lastLabel);
    }

    /**
     * Increment an exhibit label (A->B, Z->AA, AA->AB, etc.)
     */
    protected function incrementExhibitLabel($label)
    {
        // Convert to uppercase for consistency
        $label = strtoupper($label);

        // Start from the rightmost character
        $length = strlen($label);
        $position = $length - 1;

        // Create a character array from the label
        $chars = str_split($label);

        // Process right to left, carrying over as needed
        while ($position >= 0) {
            // If current character isn't Z, simply increment it and we're done
            if ($chars[$position] != 'Z') {
                $chars[$position] = chr(ord($chars[$position]) + 1);
                break;
            }

            // Current character is Z, reset to A and carry over
            $chars[$position] = 'A';
            $position--;
        }

        // If we carried over past the leftmost position, add a new 'A' at the beginning
        if ($position < 0) {
            array_unshift($chars, 'A');
        }

        return implode('', $chars);
    }

    /**
     * Get the next sort order value
     */
    protected function getNextSortOrder($caseFileId)
    {
        $maxOrder = \App\Models\Exhibit::where('case_file_id', $caseFileId)
            ->max('sort_order');

        return ($maxOrder ?? -1) + 1;
    }

    /**
     * Resequence exhibit labels after deletion
     * Call this method after deleting an exhibit if you want to maintain a clean A, B, C sequence
     */
    protected function resequenceExhibitLabels($caseFileId)
    {
        $exhibits = \App\Models\Exhibit::where('case_file_id', $caseFileId)
            ->orderBy('sort_order', 'asc')
            ->get();

        $label = 'A';
        foreach ($exhibits as $exhibit) {
            $exhibit->label = 'Exhibit ' . $label;
            $exhibit->save();
            $label = $this->incrementExhibitLabel($label);
        }
    }
}
