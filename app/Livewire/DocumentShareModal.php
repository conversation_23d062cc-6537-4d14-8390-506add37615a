<?php

namespace App\Livewire;

use App\Mail\DocumentShareMail;
use App\Models\Document;
use App\Models\DocumentShareToken;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class DocumentShareModal extends Component
{
    public $showModal = false;
    public $selectedDocuments = [];
    public $recipientEmail = '';
    public $message = '';
    public $expiresInDays = 7;
    public $isLoading = false;
    public $lastShareUrl = null;

    protected $listeners = [
        'openShareModal' => 'openModal',
        'closeShareModal' => 'closeModal',
    ];

    protected $rules = [
        'selectedDocuments' => 'required|array|min:1',
        'recipientEmail' => 'required|email',
        'message' => 'nullable|string|max:1000',
        'expiresInDays' => 'required|integer|min:1|max:30',
    ];

    protected $messages = [
        'selectedDocuments.required' => 'Please select at least one document to share.',
        'selectedDocuments.min' => 'Please select at least one document to share.',
        'recipientEmail.required' => 'Please enter a recipient email address.',
        'recipientEmail.email' => 'Please enter a valid email address.',
        'expiresInDays.min' => 'Expiration must be at least 1 day.',
        'expiresInDays.max' => 'Expiration cannot be more than 30 days.',
    ];

    public function openModal($documentIds = [])
    {
        $this->selectedDocuments = is_array($documentIds) ? $documentIds : [$documentIds];
        $this->showModal = true;
        $this->resetValidation();
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->reset(['selectedDocuments', 'recipientEmail', 'message', 'expiresInDays', 'isLoading', 'lastShareUrl']);
        $this->resetValidation();
    }

    public function toggleDocument($documentId)
    {
        if (in_array($documentId, $this->selectedDocuments)) {
            $this->selectedDocuments = array_values(array_filter($this->selectedDocuments, fn($id) => $id != $documentId));
        } else {
            $this->selectedDocuments[] = $documentId;
        }
    }

    public function shareDocuments()
    {
        $this->validate();

        $this->isLoading = true;

        try {
            // Verify user has access to all documents
            $documents = Document::whereIn('id', $this->selectedDocuments)->get();

            foreach ($documents as $document) {
                $this->authorize('view', $document);
            }

            // Create the share token
            $shareToken = DocumentShareToken::createShare(
                $this->selectedDocuments,
                $this->recipientEmail,
                $this->message,
                $this->expiresInDays
            );

            // Send the email
            Mail::to($this->recipientEmail)->send(new DocumentShareMail($shareToken));

            // Store the share URL for potential copying
            $this->lastShareUrl = route('shared.documents', ['token' => $shareToken->token]);

            Log::info('Document share email sent', [
                'share_token_id' => $shareToken->id,
                'recipient' => $this->recipientEmail,
                'document_count' => count($this->selectedDocuments),
                'sender' => auth()->id(),
                'share_url' => $this->lastShareUrl,
            ]);

            $documentCount = count($this->selectedDocuments);
            $documentText = $documentCount === 1 ? 'document' : 'documents';
            $expiryText = $this->expiresInDays === 1 ? '1 day' : "{$this->expiresInDays} days";

            session()->flash('success', "✅ {$documentCount} {$documentText} shared successfully! An email with secure download links has been sent to {$this->recipientEmail}");

            $this->closeModal();
            $this->dispatch('documentsShared');

            // Also dispatch a browser notification with more details and copy option
            $this->dispatch('show-notification', [
                'type' => 'success',
                'title' => 'Documents Shared Successfully!',
                'message' => "📧 Email sent to {$this->recipientEmail} • 🔒 Link expires in {$expiryText}",
                'duration' => 6000
            ]);

            // Dispatch event to show copy link option
            $this->dispatch('share-link-created', [
                'url' => $this->lastShareUrl,
                'recipient' => $this->recipientEmail
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to share documents', [
                'error' => $e->getMessage(),
                'recipient' => $this->recipientEmail,
                'document_ids' => $this->selectedDocuments,
            ]);

            session()->flash('error', '❌ Failed to share documents. Please try again or contact support if the problem persists.');

            // Also dispatch an error notification
            $this->dispatch('show-notification', [
                'type' => 'error',
                'title' => 'Sharing Failed',
                'message' => 'Unable to send email. Please try again.',
                'duration' => 8000
            ]);
        } finally {
            $this->isLoading = false;
        }
    }

    public function render()
    {
        return view('livewire.document-share-modal');
    }
}
