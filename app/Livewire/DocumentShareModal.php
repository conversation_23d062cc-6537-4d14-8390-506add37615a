<?php

namespace App\Livewire;

use App\Mail\DocumentShareMail;
use App\Models\Document;
use App\Models\DocumentShareToken;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class DocumentShareModal extends Component
{
    public $showModal = false;
    public $selectedDocuments = [];
    public $recipientEmail = '';
    public $message = '';
    public $expiresInDays = 7;
    public $isLoading = false;

    protected $listeners = [
        'openShareModal' => 'openModal',
        'closeShareModal' => 'closeModal',
    ];

    protected $rules = [
        'selectedDocuments' => 'required|array|min:1',
        'recipientEmail' => 'required|email',
        'message' => 'nullable|string|max:1000',
        'expiresInDays' => 'required|integer|min:1|max:30',
    ];

    protected $messages = [
        'selectedDocuments.required' => 'Please select at least one document to share.',
        'selectedDocuments.min' => 'Please select at least one document to share.',
        'recipientEmail.required' => 'Please enter a recipient email address.',
        'recipientEmail.email' => 'Please enter a valid email address.',
        'expiresInDays.min' => 'Expiration must be at least 1 day.',
        'expiresInDays.max' => 'Expiration cannot be more than 30 days.',
    ];

    public function openModal($documentIds = [])
    {
        $this->selectedDocuments = is_array($documentIds) ? $documentIds : [$documentIds];
        $this->showModal = true;
        $this->resetValidation();
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->reset(['selectedDocuments', 'recipientEmail', 'message', 'expiresInDays', 'isLoading']);
        $this->resetValidation();
    }

    public function toggleDocument($documentId)
    {
        if (in_array($documentId, $this->selectedDocuments)) {
            $this->selectedDocuments = array_values(array_filter($this->selectedDocuments, fn($id) => $id != $documentId));
        } else {
            $this->selectedDocuments[] = $documentId;
        }
    }

    public function shareDocuments()
    {
        $this->validate();

        $this->isLoading = true;

        try {
            // Verify user has access to all documents
            $documents = Document::whereIn('id', $this->selectedDocuments)->get();

            foreach ($documents as $document) {
                $this->authorize('view', $document);
            }

            // Create the share token
            $shareToken = DocumentShareToken::createShare(
                $this->selectedDocuments,
                $this->recipientEmail,
                $this->message,
                $this->expiresInDays
            );

            // Send the email
            Mail::to($this->recipientEmail)->send(new DocumentShareMail($shareToken));

            Log::info('Document share email sent', [
                'share_token_id' => $shareToken->id,
                'recipient' => $this->recipientEmail,
                'document_count' => count($this->selectedDocuments),
                'sender' => auth()->id(),
            ]);

            session()->flash('success', 'Documents shared successfully! An email has been sent to ' . $this->recipientEmail);

            $this->closeModal();
            $this->dispatch('documentsShared');

        } catch (\Exception $e) {
            Log::error('Failed to share documents', [
                'error' => $e->getMessage(),
                'recipient' => $this->recipientEmail,
                'document_ids' => $this->selectedDocuments,
            ]);

            session()->flash('error', 'Failed to share documents. Please try again.');
        } finally {
            $this->isLoading = false;
        }
    }

    public function render()
    {
        return view('livewire.document-share-modal');
    }
}
