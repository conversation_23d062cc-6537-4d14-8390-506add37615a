<?php

namespace App\Livewire\CaseFiles;

use App\Jobs\CheckResearchStatus;
use App\Models\AssistantThread;
use App\Models\CaseFile;
use App\Models\LegalResearchItem;
use App\Services\CreditService;
use App\Services\LegalResearch\GPTResearcherClient;
use App\Services\LegalResearch\ResearchItemGenerator;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class ResearchItems extends Component
{
    use AuthorizesRequests;

    public CaseFile $caseFile;
    public $items = [];
    public $isGenerating = false;
    public $errorMessage = null;
    public $researchThread = null;
    public $insufficientCredits = false;
    public $requiredCredits = 100;
    public $currentBalance = 0;

    // Create research item form properties
    public $showCreateModal = false;
    public $createForm = [
        'title' => '',
        'description' => '',
        'source_type' => 'case_law',
        'citation' => '',
        'relevance_score' => 50,
    ];

    protected $listeners = [
        'refreshResearchItems' => 'loadItems'
    ];

    public function mount(CaseFile $caseFile)
    {
        $this->caseFile = $caseFile;
        $this->loadItems();
        $this->loadResearchThread();
        $this->checkCreditBalance();
    }

    /**
     * Check if the user has enough credits for research operations
     */
    private function checkCreditBalance()
    {
        $creditService = app(CreditService::class);
        $user = Auth::user();
        if ($user) {
            $this->currentBalance = $creditService->getBalance($user);
            $this->insufficientCredits = $this->currentBalance < $this->requiredCredits;
        }
    }

    public function loadResearchThread()
    {
        // Find the latest research thread for this case
        $this->researchThread = AssistantThread::where('case_file_id', $this->caseFile->id)
            ->where('type', AssistantThread::TYPE_RESEARCH)
            ->latest()
            ->first();
    }

    public function loadItems()
    {
        $this->items = $this->caseFile->legalResearchItems()
            ->orderBy('relevance_score', 'desc')
            ->get();
    }

    public function generateItems()
    {
        $this->authorize('view', $this->caseFile);

        // Check if user has enough credits
        $this->checkCreditBalance();

        if ($this->insufficientCredits) {
            $this->errorMessage = 'Insufficient credits. You need ' . $this->requiredCredits . ' credits to generate legal research items.';
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => $this->errorMessage
            ]);
            return;
        }

        $this->isGenerating = true;
        $this->errorMessage = null;

        try {
            $researchItemGenerator = app(ResearchItemGenerator::class);
            $items = $researchItemGenerator->generateResearchItems($this->caseFile);

            $this->loadItems();
            $this->loadResearchThread();
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => __('notifications.research_items_generated', ['count' => count($items)])
            ]);

            // Refresh credit balance after operation
            $this->checkCreditBalance();
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => __('notifications.error', ['message' => $this->errorMessage])
            ]);
        }

        $this->isGenerating = false;
    }

    public function toggleFavorite(LegalResearchItem $item)
    {
        $this->authorize('update', $item->caseFile);

        $item->update([
            'is_favorited' => !$item->is_favorited
        ]);

        $this->loadItems();
    }

    public function updateStatus(LegalResearchItem $item, $status)
    {
        $this->authorize('update', $item->caseFile);

        $item->update([
            'status' => $status
        ]);

        $this->loadItems();
    }

    public function openChatInterface($threadId)
    {
        // Dispatch an event to open the chat interface with the selected thread
        $this->dispatch('openChatInterface');
        $this->dispatch('selectChatThread', $threadId);
    }

    /**
     * Initiate research for a legal research item
     *
     * @param LegalResearchItem $item
     * @return void
     */
    public function initiateResearch(LegalResearchItem $item)
    {
        $this->authorize('update', $item->caseFile);

        // Check if user has enough credits
        $this->checkCreditBalance();

        if ($this->insufficientCredits) {
            $this->errorMessage = 'Insufficient credits. You need ' . $this->requiredCredits . ' credits to initiate legal research.';
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => $this->errorMessage
            ]);
            return;
        }

        try {
            // Skip if already researching
            if (in_array($item->research_status, ['queued', 'in_progress'])) {
                $this->dispatch('notify', [
                    'type' => 'info',
                    'message' => __('notifications.research_already_in_progress', ['title' => $item->title])
                ]);
                return;
            }

            // Get credit service and user
            $creditService = app(CreditService::class);
            $user = Auth::user();

            // Deduct credits first
            $transaction = $creditService->deductCredits(
                $user,
                $this->requiredCredits,
                'Initiated legal research for: ' . $item->title,
                ['case_id' => $item->case_file_id, 'research_item_id' => $item->id]
            );

            if (!$transaction) {
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => 'Failed to deduct credits.'
                ]);
                return;
            }

            // Update status
            $item->update([
                'research_status' => 'queued',
                'research_requested_at' => now()
            ]);

            // Create research request
            $client = app(GPTResearcherClient::class);
            $result = $client->createResearchRequest($item);

            // Update item with research ID
            $item->update([
                'research_id' => $result['research_id']
            ]);

            // Schedule status check
            CheckResearchStatus::dispatch($item)->delay(now()->addMinutes(1));

            // Refresh the item
            $this->loadItems();

            // Refresh credit balance
            $this->checkCreditBalance();

            // Show notification
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => __('notifications.research_initiated', ['title' => $item->title])
            ]);

        } catch (\Exception $e) {
            // If we failed after deducting credits, refund them
            if (isset($transaction)) {
                $this->refundCredits($user, $this->requiredCredits, $item, 'Failed to initiate research: ' . $e->getMessage());
            }

            // Update item with error status
            $item->update([
                'research_status' => 'failed',
                'research_error' => $e->getMessage()
            ]);

            $this->errorMessage = $e->getMessage();
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => __('notifications.error', ['message' => $this->errorMessage])
            ]);
        }
    }

    /**
     * Refund credits to a user
     *
     * @param User $user
     * @param int $amount
     * @param LegalResearchItem $item
     * @param string $reason
     * @return void
     */
    private function refundCredits($user, int $amount, LegalResearchItem $item, string $reason): void
    {
        try {
            $creditService = app(CreditService::class);
            $creditService->addCredits(
                $user,
                $amount,
                'Refund for failed legal research: ' . $reason,
                ['case_id' => $item->case_file_id, 'research_item_id' => $item->id, 'refund_reason' => $reason]
            );

            // Log the refund
            \Illuminate\Support\Facades\Log::info('Credits refunded for failed legal research', [
                'user_id' => $user->id,
                'amount' => $amount,
                'item_id' => $item->id,
                'reason' => $reason
            ]);
        } catch (\Exception $e) {
            // Log the error but don't throw it
            \Illuminate\Support\Facades\Log::error('Failed to refund credits', [
                'user_id' => $user->id,
                'amount' => $amount,
                'item_id' => $item->id,
                'reason' => $reason,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Retry research for a failed legal research item
     *
     * @param LegalResearchItem $item
     * @return void
     */
    public function retryResearch(LegalResearchItem $item)
    {
        $this->authorize('update', $item->caseFile);

        // Check if user has enough credits
        $this->checkCreditBalance();

        if ($this->insufficientCredits) {
            $this->errorMessage = 'Insufficient credits. You need ' . $this->requiredCredits . ' credits to retry legal research.';
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => $this->errorMessage
            ]);
            return;
        }

        try {
            // Skip if not failed
            if ($item->research_status !== 'failed') {
                $this->dispatch('notify', [
                    'type' => 'info',
                    'message' => __('notifications.research_only_failed_retry')
                ]);
                return;
            }

            // Skip if no research ID
            if (!$item->research_id) {
                // Start new research instead
                $this->initiateResearch($item);
                return;
            }

            // Get credit service and user
            $creditService = app(CreditService::class);
            $user = Auth::user();

            // Deduct credits first
            $transaction = $creditService->deductCredits(
                $user,
                $this->requiredCredits,
                'Retried legal research for: ' . $item->title,
                ['case_id' => $item->case_file_id, 'research_item_id' => $item->id]
            );

            if (!$transaction) {
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => 'Failed to deduct credits.'
                ]);
                return;
            }

            // Update status
            $item->update([
                'research_status' => 'queued',
                'research_error' => null
            ]);

            // Retry research
            $client = app(GPTResearcherClient::class);
            $client->retryResearch($item->research_id);

            // Schedule status check
            CheckResearchStatus::dispatch($item)->delay(now()->addMinutes(1));

            // Refresh the item
            $this->loadItems();

            // Refresh credit balance
            $this->checkCreditBalance();

            // Show notification
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => __('notifications.research_retry_initiated', ['title' => $item->title])
            ]);

        } catch (\Exception $e) {
            // If we failed after deducting credits, refund them
            if (isset($transaction)) {
                $this->refundCredits($user, $this->requiredCredits, $item, 'Failed to retry research: ' . $e->getMessage());
            }

            // Update item with error status
            $item->update([
                'research_status' => 'failed',
                'research_error' => $e->getMessage()
            ]);

            $this->errorMessage = $e->getMessage();
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => __('notifications.error', ['message' => $this->errorMessage])
            ]);
        }
    }

    /**
     * View the research report for a legal research item
     *
     * @param LegalResearchItem $item
     * @return void
     */
    public function viewResearchReport(LegalResearchItem $item)
    {
        // Redirect to the research report URL
        if ($item->research_report_url) {
            return redirect()->away($item->research_report_url);
        } else {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => __('notifications.no_research_report')
            ]);
        }
    }

    /**
     * View the markdown content of a research report
     *
     * @param LegalResearchItem $item
     * @return void
     */
    public function viewMarkdownContent(LegalResearchItem $item)
    {

        if ($item->research_markdown_content) {
            // Show markdown content in a modal
            $this->dispatch('showMarkdownContent', title: $item->title,
                content: $item->research_markdown_content
            );
        } else {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => __('notifications.no_markdown_content')
            ]);
        }
    }

    /**
     * Delete the research report from OpenAI
     *
     * @param LegalResearchItem $item
     * @return void
     */
    public function deleteResearchFromOpenAI(LegalResearchItem $item)
    {
        $this->authorize('update', $item->caseFile);

        try {
            if ($item->deleteFromOpenAI()) {
                $this->dispatch('notify', [
                    'type' => 'success',
                    'message' => __('notifications.research_report_removed')
                ]);
            } else {
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => __('notifications.research_report_removal_failed')
                ]);
            }
        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => __('notifications.error', ['message' => $this->errorMessage])
            ]);
        }

        $this->loadItems();
    }

    /**
     * Delete a research item completely
     *
     * @param LegalResearchItem $item
     * @return void
     */
    public function deleteResearchItem(LegalResearchItem $item)
    {
        $this->authorize('update', $item->caseFile);

        try {
            // First try to delete from OpenAI if it exists
            if ($item->document_id) {
                $item->deleteFromOpenAI();
            }

            // Store the title for the notification
            $itemTitle = $item->title;

            // Delete the research item from the database
            $item->delete();

            $this->dispatch('notify', [
                'type' => 'success',
                'message' => __('notifications.research_item_deleted', ['title' => $itemTitle])
            ]);

            // Reload the items list
            $this->loadItems();

        } catch (\Exception $e) {
            $this->errorMessage = $e->getMessage();
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => __('notifications.error', ['message' => $this->errorMessage])
            ]);
        }
    }

    /**
     * Show the create research item modal
     */
    public function showCreateModal()
    {
        $this->authorize('update', $this->caseFile);

        $this->resetCreateForm();
        $this->showCreateModal = true;
    }

    /**
     * Hide the create research item modal
     */
    public function hideCreateModal()
    {
        $this->showCreateModal = false;
        $this->resetCreateForm();
    }

    /**
     * Reset the create form
     */
    public function resetCreateForm()
    {
        $this->createForm = [
            'title' => '',
            'description' => '',
            'source_type' => 'case_law',
            'citation' => '',
            'relevance_score' => 50,
        ];
    }

    /**
     * Create a new research item
     */
    public function createResearchItem()
    {
        $this->authorize('update', $this->caseFile);

        // Validate the form
        $this->validate([
            'createForm.title' => 'required|string|max:255',
            'createForm.description' => 'required|string|max:1000',
            'createForm.source_type' => 'required|string|in:case_law,statute,regulation,legal_article,legal_document,other',
            'createForm.citation' => 'nullable|string|max:255',
            'createForm.relevance_score' => 'required|integer|min:1|max:100',
        ]);

        try {
            // Create the research item
            LegalResearchItem::create([
                'case_file_id' => $this->caseFile->id,
                'title' => $this->createForm['title'],
                'description' => $this->createForm['description'],
                'source_type' => $this->createForm['source_type'],
                'citation' => $this->createForm['citation'] ?: null,
                'relevance_score' => $this->createForm['relevance_score'],
                'status' => 'active',
                'research_status' => 'not_started',
                'content_data' => [],
            ]);

            // Hide modal and reset form
            $this->hideCreateModal();

            // Reload items
            $this->loadItems();

            // Show success notification
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => __('notifications.research_item_created', ['title' => $this->createForm['title']])
            ]);

        } catch (\Exception $e) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => __('notifications.error', ['message' => $e->getMessage()])
            ]);
        }
    }

    public function render()
    {
        return view('livewire.case-files.research-items');
    }
}
