<?php

namespace App\Livewire\CaseFiles;

use App\Models\CaseFile;
use Livewire\Component;
use Illuminate\Support\Str;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class Show extends Component
{
    use AuthorizesRequests;

    public CaseFile $caseFile;

    public function mount(CaseFile $caseFile)
    {
        // Eager load the relationships needed for the badges
        $this->caseFile = $caseFile->load([
            'legalResearchItems',
            'strategies',
            'drafts'
        ]);
    }

    public function enableCollaboration()
    {
        $this->authorize('update', $this->caseFile);

        $this->caseFile->update([
            'collaboration_enabled' => true,
            'max_collaborators' => 5 // Default value
        ]);

        $this->dispatch('notify', [
            'message' => __('collaboration.notifications.enabled'),
            'type' => 'success'
        ]);
    }

    public function openChat()
    {
        $this->dispatch('openChatForCase', $this->caseFile->id);
    }

    public function createInvoice()
    {
        // Redirect to the invoice creation page with the case file ID pre-selected
        return redirect()->route('invoices.create', ['case_file_id' => $this->caseFile->id]);
    }

    public function render()
    {
        // Check if the user has a Connect account that can accept payments
        $canCreateInvoices = false;
        $connectAccount = auth()->user()->connectAccount;

        if ($connectAccount && $connectAccount->charges_enabled && $connectAccount->details_submitted) {
            $canCreateInvoices = true;
        }

        return view('livewire.case-files.show', [
            'canCreateInvoices' => $canCreateInvoices
        ]);
    }
}
