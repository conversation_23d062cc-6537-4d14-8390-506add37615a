<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\Attributes\Modelable;

/**
 * Purpose: The VoiceMessageInput is intended to be a portable component that allows a person to speak their thoughts and have them transcribed to be pasted in a textarea
 */
class VoiceMessageInput extends Component
{
    use WithFileUploads;

    /**
     * The current message content in the textarea
     * @var string
     */
    #[Modelable]
    public $message = '';

    /**
     * The height of the textarea element
     * @var string
     */
    public $height = '150px';

    /**
     * The name attribute for the textarea element
     * @var string
     */
    public $name;

    /**
     * Indicates whether audio recording is in progress
     * @var bool
     */
    public $isRecording = false;

    /**
     * Controls whether the file upload button is displayed
     * @var bool
     */
    public $showFileUpload = true;

    /**
     * Controls whether the send button is displayed
     * @var bool
     */
    public $showSendButton = true;

    /**
     * The text for the send button
     * @var string|null
     */
    public $sendButtonText = null;

    /**
     * The endpoint to which the message and files will be sent
     * @var string|null
     */
    public $endpoint = null;

    /**
     * Initialize the component with required parameters
     */
    public function mount($name, $height = null, $value = '', $showFileUpload = false, $showSendButton = false, $sendButtonText = null, $endpoint = null)
    {
        $this->name = $name;
        if ($height) {
            $this->height = $height;
        }
        $this->message = $value;
        $this->showFileUpload = $showFileUpload;
        $this->showSendButton = $showSendButton;
        $this->sendButtonText = $sendButtonText;
        $this->endpoint = $endpoint;
    }

    /**
     * Handle form submission with message and files
     */
    public function submit()
    {

        // Dispatch event with message and files data
        $this->dispatch('voice-message-submitted', [
            'name' => $this->name,
            'message' => $this->message,
        ]);

        // Clear the message
        $this->message = '';

        return [
            'success' => true,
            'message' => 'Message submitted successfully'
        ];
    }

    /**
     * Update the parent component when the message changes
     * Using debounce to reduce update frequency
     */
    public function updatedMessage($value)
    {
        // Dispatch event for components that listen to it
        $this->dispatch('voice-message-updated', name: $this->name, message: $value);
    }

    /**
     * Append transcribed text to the existing message
     */
    public function appendTranscription($transcription): void
    {
        $this->message = trim($this->message . "\n" . $transcription);
        $this->dispatch('voice-message-updated', name: $this->name, message: $this->message);
    }

    /**
     * Render the component view
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('livewire.voice-message-input', [
            'attributes' => $this->attributes
        ]);
    }
}
