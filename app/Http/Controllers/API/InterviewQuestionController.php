<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\CaseFile;
use App\Models\InterviewQuestion;
use App\Services\OpenAI\AIQuestionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class InterviewQuestionController extends Controller
{
    protected $aiQuestionService;

    public function __construct(AIQuestionService $aiQuestionService)
    {
        $this->aiQuestionService = $aiQuestionService;
    }

    /**
     * Generate questions for a case file
     *
     * @param Request $request
     * @param CaseFile $caseFile
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateQuestions(Request $request, CaseFile $caseFile)
    {
        try {
            // Check if the user has access to this case file
            if ($caseFile->user_id !== $request->user()->id) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Generate questions
            $questions = $this->aiQuestionService->generateQuestionsForCase($caseFile);

            return response()->json([
                'success' => true,
                'questions' => $questions
            ]);
        } catch (\Exception $e) {
            Log::error('Error generating questions: ' . $e->getMessage(), [
                'case_id' => $caseFile->id,
                'user_id' => $request->user()->id
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to generate questions: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get questions for a case file
     *
     * @param Request $request
     * @param CaseFile $caseFile
     * @return \Illuminate\Http\JsonResponse
     */
    public function getQuestions(Request $request, CaseFile $caseFile)
    {
        // Check if the user has access to this case file
        if ($caseFile->user_id !== $request->user()->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $questions = $caseFile->interviewQuestions()
            ->orderBy('question_order')
            ->get();

        return response()->json([
            'success' => true,
            'questions' => $questions
        ]);
    }

    /**
     * Submit an answer to a question
     *
     * @param Request $request
     * @param InterviewQuestion $question
     * @return \Illuminate\Http\JsonResponse
     */
    public function submitAnswer(Request $request, InterviewQuestion $question)
    {
        try {
            // Check if the user has access to this question's case file
            $caseFile = $question->caseFile;
            if ($caseFile->user_id !== $request->user()->id) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Validate the request
            $request->validate([
                'answer' => 'required',
                'document_id' => 'nullable|exists:documents,id'
            ]);

            // Update the question
            $question->update([
                'answer' => $request->answer,
                'document_id' => $request->document_id,
                'is_answered' => true
            ]);

            return response()->json([
                'success' => true,
                'question' => $question
            ]);
        } catch (\Exception $e) {
            Log::error('Error submitting answer: ' . $e->getMessage(), [
                'question_id' => $question->id,
                'user_id' => $request->user()->id
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to submit answer: ' . $e->getMessage()
            ], 500);
        }
    }
}