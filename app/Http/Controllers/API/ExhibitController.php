<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CaseFile;
use App\Models\Exhibit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ExhibitController extends Controller
{
    /**
     * Get documents that can be used as exhibits for a case file
     *
     * @param CaseFile $caseFile
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(CaseFile $caseFile)
    {
        try {
            // Log the request
            \Illuminate\Support\Facades\Log::info('Fetching documents for exhibits', [
                'case_file_id' => $caseFile->id,
                'user_id' => Auth::id()
            ]);

            // Check if the user has access to the case file
            if (Auth::id() !== $caseFile->user_id) {
                \Illuminate\Support\Facades\Log::warning('Unauthorized access to exhibits', [
                    'case_file_id' => $caseFile->id,
                    'user_id' => Auth::id(),
                    'case_file_user_id' => $caseFile->user_id
                ]);
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            // Get documents directly from the case file
            $documents = $caseFile->documents()
                ->whereNotNull('storage_path')
                ->where(function($query) {
                    $query->where('mime_type', 'like', 'application/pdf')
                          ->orWhere('mime_type', 'like', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
                          ->orWhere('mime_type', 'like', 'image/%');
                })
                ->get()
                ->map(function ($document) {
                    // Create a temporary URL for the document
                    $temporaryUrl = null;
                    try {
                        if (\Illuminate\Support\Facades\Storage::exists($document->storage_path)) {
                            $temporaryUrl = \Illuminate\Support\Facades\Storage::temporaryUrl(
                                $document->storage_path,
                                now()->addMinutes(5)
                            );
                        }
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::warning('Failed to create temporary URL', [
                            'document_id' => $document->id,
                            'error' => $e->getMessage()
                        ]);
                    }

                    return [
                        'id' => $document->id,
                        'title' => $document->title ?? $document->original_filename ?? 'Untitled',
                        'document_type' => $document->mime_type ?? 'Unknown',
                        'file_size' => $document->file_size ?? 0,
                        'temporary_url' => $temporaryUrl,
                        'is_exhibit' => $document->exhibit()->exists(),
                        'exhibit_id' => $document->exhibit ? $document->exhibit->id : null,
                    ];
                });

            // Log the result
            \Illuminate\Support\Facades\Log::info('Documents fetched successfully', [
                'case_file_id' => $caseFile->id,
                'document_count' => $documents->count()
            ]);

            return response()->json([
                'exhibits' => $documents
            ]);
        } catch (\Exception $e) {
            // Log the error
            \Illuminate\Support\Facades\Log::error('Error fetching exhibits', [
                'case_file_id' => $caseFile->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch exhibits',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
