<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Services\ConnectService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ConnectController extends Controller
{
    protected $connectService;

    public function __construct(ConnectService $connectService)
    {
        $this->connectService = $connectService;
    }

    /**
     * Show the Connect account dashboard.
     */
    public function index()
    {
        $user = Auth::user();
        $connectAccount = $user->connectAccount;

        // If the account exists but is pending, check for a remediation URL
        $remediationUrl = null;
        if ($connectAccount && $connectAccount->details_submitted && !$connectAccount->charges_enabled) {

            // Sync with <PERSON><PERSON> to ensure we have the latest requirements
            $connectAccount->syncFromStripe();
            $remediationUrl = $connectAccount->getRemediationUrl();

        }

        return view('connect.index', [
            'user' => $user,
            'connectAccount' => $connectAccount,
            'remediationUrl' => $remediationUrl ?? session('remediationUrl'),
        ]);
    }

    /**
     * Start the onboarding process for a Connect account.
     */
    public function startOnboarding(Request $request)
    {
        $user = Auth::user();

        try {
            // Create a Connect account if the user doesn't have one
            if (!$user->connectAccount) {
                $connectAccount = $this->connectService->createConnectAccount($user);
            } else {
                $connectAccount = $user->connectAccount;
            }

            // Generate an onboarding URL
            $returnUrl = route('connect.complete');
            $onboardingUrl = $this->connectService->generateOnboardingUrl($connectAccount, $returnUrl);

            if (!$onboardingUrl) {
                return back()->withErrors(['error' => 'Failed to generate onboarding URL.']);
            }

            // Redirect to the onboarding URL
            return redirect($onboardingUrl);
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to start onboarding: ' . $e->getMessage()]);
        }
    }

    /**
     * Handle the completion of the onboarding process.
     */
    public function completeOnboarding()
    {
        $user = Auth::user();
        $connectAccount = $user->connectAccount;

        if (!$connectAccount) {
            return redirect()->route('connect.index')
                ->withErrors(['error' => 'No Connect account found.']);
        }

        try {
            // Manually retrieve the account from Stripe to ensure we have the latest status
            // This serves as a fallback in case webhooks fail
            $stripe = new \Stripe\StripeClient(config('cashier.secret'));
            $stripeAccount = $stripe->accounts->retrieve($connectAccount->stripe_account_id);

            // Update the local record with the latest status
            $connectAccount->charges_enabled = $stripeAccount->charges_enabled;
            $connectAccount->payouts_enabled = $stripeAccount->payouts_enabled;
            $connectAccount->details_submitted = $stripeAccount->details_submitted;
            $connectAccount->requirements = $stripeAccount->requirements->toArray();
            $connectAccount->save();

            \Illuminate\Support\Facades\Log::info('Connect account manually synced after onboarding', [
                'user_id' => $user->id,
                'account_id' => $connectAccount->stripe_account_id,
                'charges_enabled' => $connectAccount->charges_enabled,
                'details_submitted' => $connectAccount->details_submitted
            ]);

            if ($connectAccount->charges_enabled && $connectAccount->details_submitted) {
                return redirect()->route('connect.index')
                    ->with('success', 'Your account is now ready to accept payments!');
            } else {
                // Check if there's a remediation URL available
                $remediationUrl = $connectAccount->getRemediationUrl();

                return redirect()->route('connect.index')
                    ->with('info', 'Your account setup is in progress. Some information may still be required.')
                    ->with('remediationUrl', $remediationUrl);
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Failed to complete onboarding', [
                'user_id' => $user->id,
                'account_id' => $connectAccount->stripe_account_id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('connect.index')
                ->withErrors(['error' => 'Failed to complete onboarding: ' . $e->getMessage()]);
        }
    }

    /**
     * Refresh the onboarding process.
     */
    public function refreshOnboarding(User $user)
    {
        // Security check - only allow users to refresh their own onboarding
        if (Auth::id() !== $user->id) {
            abort(403);
        }

        $connectAccount = $user->connectAccount;

        if (!$connectAccount) {
            return redirect()->route('connect.index')
                ->withErrors(['error' => 'No Connect account found.']);
        }

        try {
            // Generate a new onboarding URL
            $returnUrl = route('connect.complete');
            $onboardingUrl = $this->connectService->generateOnboardingUrl($connectAccount, $returnUrl);

            if (!$onboardingUrl) {
                return redirect()->route('connect.index')
                    ->withErrors(['error' => 'Failed to generate onboarding URL.']);
            }

            // Redirect to the onboarding URL
            return redirect($onboardingUrl);
        } catch (\Exception $e) {
            return redirect()->route('connect.index')
                ->withErrors(['error' => 'Failed to refresh onboarding: ' . $e->getMessage()]);
        }
    }

    /**
     * View the Stripe dashboard for a Connect account.
     */
    public function viewDashboard()
    {
        $user = Auth::user();
        $connectAccount = $user->connectAccount;

        if (!$connectAccount) {
            return redirect()->route('connect.index')
                ->withErrors(['error' => 'No Connect account found.']);
        }

        try {
            // Generate a dashboard URL
            $dashboardUrl = $connectAccount->getDashboardUrl();

            if (!$dashboardUrl) {
                return redirect()->route('connect.index')
                    ->withErrors(['error' => 'Failed to generate dashboard URL.']);
            }

            // Redirect to the dashboard URL
            return redirect($dashboardUrl);
        } catch (\Exception $e) {
            return redirect()->route('connect.index')
                ->withErrors(['error' => 'Failed to access dashboard: ' . $e->getMessage()]);
        }
    }
}
