<?php

namespace App\Http\Controllers;

use App\Models\CreditProduct;
use App\Services\CreditService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CreditController extends Controller
{
    protected $creditService;

    public function __construct(CreditService $creditService)
    {
        $this->creditService = $creditService;
    }

    /**
     * Display the credit dashboard.
     */
    public function index()
    {
        $user = Auth::user();
        $balance = $this->creditService->getBalance($user);
        $transactions = $this->creditService->getTransactionHistory($user, 10);
        $products = CreditProduct::active()->get();

        // Check if user has an active subscription
        $hasActiveSubscription = $user->subscribed('default');

        return view('credits.index', compact('balance', 'transactions', 'products', 'hasActiveSubscription'));
    }

    /**
     * Show the form for purchasing credits.
     */
    public function showPurchaseForm()
    {
        $user = Auth::user();

        // Check if user has an active subscription
        if (!$user->subscribed('default')) {
            return redirect()->route('credits.index')
                ->with('error', 'You must have an active subscription to purchase additional credits. Please subscribe to a plan first.');
        }

        $products = CreditProduct::active()->get();
        return view('credits.purchase', compact('products'));
    }

    /**
     * Process a credit purchase.
     */
    public function purchase(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:credit_products,id',
            'payment_method_id' => 'required|string',
        ]);

        $user = Auth::user();

        // Check if user has an active subscription
        if (!$user->subscribed('default')) {
            return redirect()->route('credits.index')
                ->with('error', 'You must have an active subscription to purchase additional credits. Please subscribe to a plan first.');
        }

        $product = CreditProduct::findOrFail($request->product_id);

        try {
            // Make sure the user has a Stripe customer ID
            $user->createOrGetStripeCustomer();

            // Use Laravel Cashier's payment method to make a simple charge
            $payment = $user->charge(
                $product->price_cents, // amount in cents
                $request->payment_method_id,
                [
                    'description' => "Purchase of {$product->name}",
                    'metadata' => [
                        'product_id' => $product->id,
                        'credits' => $product->credits,
                    ],
                    'return_url' => route('credits.purchase.complete'),
                ]
            );

            // If we get here, the payment was successful
            // Add credits to the user's account
            $this->creditService->addCredits(
                $user,
                $product->credits,
                "Purchased {$product->name}",
                [
                    'product_id' => $product->id,
                    'payment_id' => $payment->id,
                ]
            );

            return redirect()->route('credits.index')
                ->with('success', "Successfully purchased {$product->credits} credits!");
        } catch (\Laravel\Cashier\Exceptions\IncompletePayment $exception) {
            // This exception is thrown when the payment needs additional confirmation
            // Store the payment intent and product ID in the session
            session(['pending_payment_intent_id' => $exception->payment->id]);
            session(['pending_product_id' => $product->id]);

            // Redirect to the payment confirmation page
            return redirect()->route(
                'cashier.payment',
                [$exception->payment->id, 'redirect' => route('credits.purchase.complete')]
            );
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Payment failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Handle the completion of a credit purchase after redirect.
     */
    public function completePayment(Request $request)
    {
        $user = Auth::user();
        $paymentIntentId = session('pending_payment_intent_id');
        $productId = session('pending_product_id');

        if (!$paymentIntentId || !$productId) {
            return redirect()->route('credits.index')
                ->withErrors(['error' => 'Payment session expired or invalid.']);
        }

        try {
            // Clear the session data
            session()->forget(['pending_payment_intent_id', 'pending_product_id']);

            // Get the product
            $product = CreditProduct::findOrFail($productId);

            // Retrieve the payment intent from Stripe
            $paymentIntent = $user->stripe()->paymentIntents->retrieve($paymentIntentId);

            // Check if the payment was successful
            if ($paymentIntent->status === 'succeeded') {
                // Check if credits have already been added for this payment
                $existingTransaction = $user->creditTransactions()
                    ->where('metadata->payment_intent_id', $paymentIntentId)
                    ->first();

                if (!$existingTransaction) {
                    // Add credits to the user's account
                    $this->creditService->addCredits(
                        $user,
                        $product->credits,
                        "Purchased {$product->name}",
                        [
                            'product_id' => $product->id,
                            'payment_intent_id' => $paymentIntentId,
                        ]
                    );
                }

                return redirect()->route('credits.index')
                    ->with('success', "Successfully purchased {$product->credits} credits!");
            } else {
                return redirect()->route('credits.purchase.form')
                    ->withErrors(['error' => 'Payment was not completed successfully. Status: ' . $paymentIntent->status]);
            }
        } catch (\Exception $e) {
            return redirect()->route('credits.purchase.form')
                ->withErrors(['error' => 'Error processing payment: ' . $e->getMessage()]);
        }
    }

    /**
     * Display the user's transaction history.
     */
    public function transactions()
    {
        $user = Auth::user();
        $transactions = $user->creditTransactions()
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('credits.transactions', compact('transactions'));
    }
}
