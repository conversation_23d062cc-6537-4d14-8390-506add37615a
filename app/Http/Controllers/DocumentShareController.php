<?php

namespace App\Http\Controllers;

use App\Mail\DocumentShareMail;
use App\Models\Document;
use App\Models\DocumentShareToken;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Response;

class DocumentShareController extends Controller
{
    /**
     * Create a new document share
     */
    public function store(Request $request)
    {
        $request->validate([
            'document_ids' => 'required|array|min:1',
            'document_ids.*' => 'exists:documents,id',
            'recipient_email' => 'required|email',
            'message' => 'nullable|string|max:1000',
            'expires_in_days' => 'nullable|integer|min:1|max:30',
        ]);

        // Verify user has access to all documents
        $documents = Document::whereIn('id', $request->document_ids)->get();

        foreach ($documents as $document) {
            $this->authorize('view', $document);
        }

        // Create the share token
        $shareToken = DocumentShareToken::createShare(
            $request->document_ids,
            $request->recipient_email,
            $request->message,
            $request->expires_in_days ?? 7
        );

        // Send the email
        try {
            Mail::to($request->recipient_email)->send(new DocumentShareMail($shareToken));

            Log::info('Document share email sent', [
                'share_token_id' => $shareToken->id,
                'recipient' => $request->recipient_email,
                'document_count' => count($request->document_ids),
                'sender' => auth()->id(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Documents shared successfully! An email has been sent to ' . $request->recipient_email,
                'share_token' => $shareToken->token,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to send document share email', [
                'error' => $e->getMessage(),
                'share_token_id' => $shareToken->id,
                'recipient' => $request->recipient_email,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Documents were prepared for sharing, but the email could not be sent. Please try again.',
            ], 500);
        }
    }

    /**
     * Show the public document share page
     */
    public function show(string $token)
    {
        // Rate limiting for public access
        $key = 'document-share-access:' . request()->ip();
        if (RateLimiter::tooManyAttempts($key, 10)) {
            abort(429, 'Too many attempts. Please try again later.');
        }
        RateLimiter::hit($key, 300); // 5 minutes

        $shareToken = DocumentShareToken::where('token', $token)->first();

        if (!$shareToken) {
            abort(404, 'Share link not found.');
        }

        if (!$shareToken->isValid()) {
            $reason = $shareToken->isExpired() ? 'expired' :
                     ($shareToken->hasReachedAccessLimit() ? 'access_limit_reached' : 'inactive');

            return view('shared.documents.invalid', [
                'reason' => $reason,
                'shareToken' => $shareToken,
            ]);
        }

        // Record the access
        $shareToken->recordAccess();

        // Get the documents
        $documents = $shareToken->documents()->get();

        return view('shared.documents.show', [
            'shareToken' => $shareToken,
            'documents' => $documents,
            'sender' => $shareToken->creator,
        ]);
    }

    /**
     * Download a specific document from a share
     */
    public function download(string $token, int $documentId)
    {
        // Rate limiting
        $key = 'document-download:' . request()->ip();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            abort(429, 'Too many download attempts. Please try again later.');
        }
        RateLimiter::hit($key, 60); // 1 minute

        $shareToken = DocumentShareToken::where('token', $token)->first();

        if (!$shareToken || !$shareToken->isValid()) {
            abort(404, 'Share link not found or expired.');
        }

        // Verify the document is part of this share
        if (!in_array($documentId, $shareToken->document_ids)) {
            abort(403, 'Document not included in this share.');
        }

        $document = Document::findOrFail($documentId);

        // Generate temporary S3 URL (longer duration for media files)
        $duration = $this->getDownloadDuration($document->mime_type);

        try {
            $temporaryUrl = Storage::disk('s3')->temporaryUrl(
                $document->storage_path,
                now()->addMinutes($duration)
            );

            Log::info('Document downloaded via share', [
                'share_token_id' => $shareToken->id,
                'document_id' => $documentId,
                'ip' => request()->ip(),
            ]);

            return redirect($temporaryUrl);
        } catch (\Exception $e) {
            Log::error('Failed to generate download URL', [
                'error' => $e->getMessage(),
                'document_id' => $documentId,
                'share_token_id' => $shareToken->id,
            ]);

            abort(500, 'Unable to generate download link.');
        }
    }

    /**
     * Get appropriate download duration based on file type
     */
    private function getDownloadDuration(string $mimeType): int
    {
        // Longer duration for media files that might be streamed
        if (str_starts_with($mimeType, 'video/') || str_starts_with($mimeType, 'audio/')) {
            return 60; // 1 hour for media files
        }

        return 10; // 10 minutes for documents
    }
}
