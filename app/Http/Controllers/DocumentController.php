<?php

namespace App\Http\Controllers;

use App\Models\CaseFile;
use App\Models\Draft;
use App\Models\Document;
use App\Jobs\GenerateDocumentJob;
use App\Services\DocumentGenerationService;
use App\Services\EnhancedDocumentGenerationService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;

class DocumentController extends Controller
{
    use AuthorizesRequests;

    protected $documentGenerationService;
    protected $enhancedDocumentGenerationService;

    public function __construct(
        ?DocumentGenerationService $documentGenerationService = null,
        ?EnhancedDocumentGenerationService $enhancedDocumentGenerationService = null
    ) {
        $this->documentGenerationService = $documentGenerationService ?? app(DocumentGenerationService::class);
        $this->enhancedDocumentGenerationService = $enhancedDocumentGenerationService ?? app(EnhancedDocumentGenerationService::class);
    }

    public function show(Document $document): StreamedResponse
    {
        $this->authorize('view', $document);

        return Storage::response($document->storage_path, $document->original_filename, [
            'Content-Type' => $document->mime_type,
            'Content-Disposition' => 'inline; filename="' . $document->original_filename . '"'
        ]);
    }

    /**
     * Generate a document from a draft
     */
    public function generate(Request $request, Draft $draft)
    {
        $this->authorize('generateDocument', $draft);

        // Validate request
        $request->validate([
            'format' => 'nullable|in:docx,pdf',
            'options' => 'nullable|array',
            'exhibits' => 'nullable|string',
        ]);

        // Set default format
        $format = $request->input('format', 'docx');

        // Get options
        $options = $request->input('options', []);

        // Process documents to use as exhibits if provided
        $selectedDocuments = [];
        if ($request->has('selected_exhibits')) {
            try {
                $selectedDocuments = $request->input('selected_exhibits');
                if (!is_array($selectedDocuments)) {
                    $selectedDocuments = [];
                }
            } catch (\Exception $e) {
                \Illuminate\Support\Facades\Log::warning('Failed to parse selected exhibits', [
                    'error' => $e->getMessage(),
                    'exhibits' => $request->input('selected_exhibits')
                ]);
                $selectedDocuments = [];
            }
        }

        try {
            // Make sure the storage directory exists
            if (!file_exists(storage_path('app/public/documents'))) {
                mkdir(storage_path('app/public/documents'), 0755, true);
            }

            // Define optional fields that might not exist in the database schema
            $optionalFields = [
                'document_type' => 'generated',
                'created_by' => Auth::id(),
                'metadata' => [
                    'generated_from' => 'draft',
                    'draft_id' => $draft->id,
                    'generation_date' => now()->toDateTimeString(),
                    'options' => $options
                ]
            ];

            $caseFileId = $draft->case_file_id;

            // Dispatch a job to generate the document in the background
            GenerateDocumentJob::dispatch($draft, $options, $selectedDocuments, Auth::id(), $optionalFields);

            // Return success response with redirect
            return redirect()->route('case-files.documents.index', [
                'case_file' => $caseFileId,
            ])->with('success', 'Document generation has started. You will be notified when it is complete.');
        } catch (\Exception $e) {
            // Log the error
            Log::error('Document generation error', [
                'draft_id' => $draft->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return error response
            return back()->with('error', 'Error generating document: ' . $e->getMessage());
        }
    }

    /**
     * Download a document
     */
    public function download(Document $document)
    {
        $this->authorize('view', $document);

        // Log the document details for debugging
        Log::info('Document download attempt', [
            'document_id' => $document->id,
            'storage_path' => $document->storage_path,
            'file_exists' => Storage::exists($document->storage_path)
        ]);

        // Check if file exists
        if (!Storage::exists($document->storage_path)) {
            // Try without the 'public/' prefix
            $altPath = str_replace('public/', '', $document->storage_path);

            Log::info('Trying alternative path', [
                'alt_path' => $altPath,
                'file_exists' => Storage::exists($altPath)
            ]);

            if (Storage::exists($altPath)) {
                // Return file download with alternative path
                return Storage::download(
                    $altPath,
                    $document->original_filename,
                    ['Content-Type' => $document->mime_type]
                );
            }

            abort(404, 'Document file not found');
        }

        // Return file download
        return Storage::download(
            $document->storage_path,
            $document->original_filename,
            ['Content-Type' => $document->mime_type]
        );
    }
}
