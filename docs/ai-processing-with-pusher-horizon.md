# AI Processing with <PERSON><PERSON><PERSON> and <PERSON>

This document explains how the application uses <PERSON><PERSON><PERSON> and <PERSON>vel Horizon to process AI chats and responses for efficiency and scalability.

## Overview

The application uses:
- **Laravel Horizon**: For managing and monitoring Redis queues
- **Pusher**: For real-time broadcasting of AI responses
- **Redis**: As the queue driver for background processing

## How It Works

1. When a user sends a message to the AI assistant in the document editor:
   - The message is immediately displayed in the chat
   - A "Thinking..." message is shown
   - The request is dispatched to a background job (`ProcessAiResponseJob`)

2. The job processes the AI request asynchronously:
   - It runs on a dedicated `ai-processing` queue
   - It has a longer timeout (5 minutes) to accommodate complex AI requests
   - When complete, it broadcasts the result via Pusher

3. The client receives the response in real-time:
   - The "Thinking..." message is replaced with the actual response
   - Any document content is processed and displayed

## Configuration

### Pusher Configuration

Pusher is configured in the `.env` file:

```
BROADCAST_DRIVER=pusher
PUSHER_APP_ID=your_app_id
PUSHER_APP_KEY=your_app_key
PUSHER_APP_SECRET=your_app_secret
PUSHER_APP_CLUSTER=your_app_cluster
```

### Queue Configuration

Redis queues are configured in the `.env` file:

```
QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379
```

### Horizon Configuration

Horizon is configured in `config/horizon.php` with dedicated supervisors for AI processing:

- `supervisor-1`: Handles general application jobs
- `ai-supervisor`: Specifically configured for AI processing jobs with longer timeouts

## Running the System

1. Start Laravel Horizon:

```bash
php artisan horizon
```

2. Monitor the queues in the Horizon dashboard:

```
https://your-app-url/horizon
```

## Troubleshooting

If AI responses are not being received:

1. Check that Horizon is running
2. Verify Pusher credentials in the `.env` file
3. Check the Laravel logs for any errors
4. Ensure Redis is running and accessible
5. Check the Horizon dashboard for failed jobs

## Security

- All Pusher channels for AI responses are private channels
- Authentication is handled through Laravel's broadcasting authentication system
- Only users with access to a draft can subscribe to its channel
