// Import Quill
import Quill from 'quill';

// Initialize Quill editor
window.initQuillEditor = function(elementId, content, updateCallback, options = {}) {
    // Default options
    const defaultOptions = {
        autoSave: true,           // Enable auto-save by default
        autoSaveDelay: 2000,      // Auto-save delay in milliseconds (2 seconds)
        showSaveIndicator: true   // Show save indicator
    };

    // Merge default options with provided options
    const editorOptions = { ...defaultOptions, ...options };
    // Define toolbar options - limited to font weight, family, size, alignment, and color
    const toolbarOptions = [
        ['bold', 'italic'],                                // font weight/style
        [{ 'size': ['small', false, 'large', 'huge'] }],  // font size
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'font': [] }],                                 // font family
        [{ 'align': [] }],                                // alignment
        [{ 'color': [] }, { 'background': [] }],          // color
        ['clean']                                         // remove formatting button
    ];


    // ['bold', 'italic'],                                // font weight/style
    //     [{ 'size': ['small', false, 'large', 'huge'] }],  // font size
    //     [{ 'font': [] }],                                 // font family
    //     [{ 'align': [] }],                                // alignment
    //     [{ 'color': [] }, { 'background': [] }],          // color
    //     ['clean']

    // Initialize Quill
    const quill = new Quill(`#${elementId}`, {
        modules: {
            toolbar: toolbarOptions
        },
        theme: 'snow'  // Use the snow theme (clean, modern look)
    });

    // Set initial content if provided
    if (content) {
        try {
            console.log('Setting initial Quill content:', content);

            // Try to parse as JSON if it's a string that starts with {
            if (typeof content === 'string' && content.trim().startsWith('{')) {
                try {
                    // Parse the JSON string
                    const jsonObj = JSON.parse(content);

                    // Check if it's a Delta object (has 'ops' property)
                    if (jsonObj.ops) {
                        console.log('Setting Quill contents with Delta object');
                        quill.setContents(jsonObj);
                    }
                    // Check if it's an EditorJS object (has 'blocks' property)
                    else if (jsonObj.blocks && Array.isArray(jsonObj.blocks)) {
                        console.log('Detected EditorJS format, converting to Quill Delta');
                        // Use the converter function if available
                        if (typeof window.convertEditorJSToQuill === 'function') {
                            const delta = window.convertEditorJSToQuill(jsonObj);
                            console.log('Converted EditorJS to Quill Delta:', delta);
                            quill.setContents(delta);
                        } else {
                            console.warn('EditorJS converter not available, using as text');
                            // Extract text from blocks as fallback
                            let extractedText = '';
                            jsonObj.blocks.forEach(block => {
                                if (block.data && block.data.text) {
                                    extractedText += block.data.text + '\n\n';
                                }
                            });
                            quill.setText(extractedText || content);
                        }
                    } else {
                        // It's JSON but not a recognized format
                        console.log('JSON is not a recognized format, using as text');
                        quill.setText(content);
                    }
                } catch (e) {
                    console.error('Error parsing JSON:', e);
                    // If parsing fails, set as HTML
                    quill.root.innerHTML = content;
                }
            } else {
                // Set as HTML
                console.log('Setting content as HTML');
                quill.root.innerHTML = content;
            }
        } catch (error) {
            console.error('Error setting Quill content:', error);
            // Fallback to empty content
            quill.setText('');
        }
    }

    // Create save indicator element if enabled
    let saveIndicator = null;
    if (editorOptions.showSaveIndicator) {
        saveIndicator = document.createElement('div');
        saveIndicator.className = 'quill-save-indicator';
        saveIndicator.style.cssText = 'position: absolute; right: 10px; bottom: 10px; font-size: 12px; color: #666; display: none;';
        document.getElementById(elementId).parentNode.style.position = 'relative';
        document.getElementById(elementId).parentNode.appendChild(saveIndicator);
    }

    // Create a debounced save function
    let saveTimeout = null;
    let isSaving = false;

    const saveContent = () => {
        if (typeof updateCallback === 'function') {
            // Show saving indicator if enabled
            if (saveIndicator) {
                saveIndicator.textContent = 'Saving...';
                saveIndicator.style.display = 'block';
            }

            isSaving = true;

            // Get content as Delta object (preserves all formatting)
            const delta = quill.getContents();
            // Also get HTML for display purposes
            const html = quill.root.innerHTML;

            // Call the update callback with both formats
            const result = updateCallback({
                delta: delta,
                html: html
            });

            // Handle promise if returned
            if (result instanceof Promise) {
                result.finally(() => {
                    isSaving = false;
                    if (saveIndicator) {
                        saveIndicator.textContent = 'Saved';
                        // Hide the indicator after 2 seconds
                        setTimeout(() => {
                            saveIndicator.style.display = 'none';
                        }, 2000);
                    }
                });
            } else {
                // If not a promise, assume it completed
                isSaving = false;
                if (saveIndicator) {
                    saveIndicator.textContent = 'Saved';
                    // Hide the indicator after 2 seconds
                    setTimeout(() => {
                        saveIndicator.style.display = 'none';
                    }, 2000);
                }
            }
        }
    };

    // Debounced save function
    const debouncedSave = () => {
        // Clear any existing timeout
        if (saveTimeout) {
            clearTimeout(saveTimeout);
        }

        // Set a new timeout
        if (editorOptions.autoSave) {
            saveTimeout = setTimeout(() => {
                saveContent();
            }, editorOptions.autoSaveDelay);
        }
    };

    // Set up content change handler
    quill.on('text-change', function() {
        // Trigger debounced save
        debouncedSave();
    });

    // Add manual save method to the quill instance
    quill.save = saveContent;

    // Add method to toggle auto-save
    quill.toggleAutoSave = (enable) => {
        editorOptions.autoSave = enable;

        // If enabling auto-save, trigger a save immediately
        if (enable && !isSaving) {
            debouncedSave();
        }

        return editorOptions.autoSave;
    };

    // Store the Quill instance globally for access from other scripts
    if (!window.quillInstances) {
        window.quillInstances = {};
    }
    window.quillInstances[elementId] = quill;

    return quill;
};

// Helper function to insert text at cursor position
window.quillInsertText = function(editorId, text) {
    if (window.quillInstances && window.quillInstances[editorId]) {
        const quill = window.quillInstances[editorId];
        const range = quill.getSelection();

        if (range) {
            // Insert at current selection
            quill.insertText(range.index, text);
        } else {
            // If no selection, insert at end
            quill.insertText(quill.getLength() - 1, text);
        }

        return true;
    }

    return false;
};

// Helper function to insert HTML at cursor position
window.quillInsertHTML = function(editorId, html) {
    if (window.quillInstances && window.quillInstances[editorId]) {
        const quill = window.quillInstances[editorId];
        const range = quill.getSelection();

        if (range) {
            // Insert at current selection
            quill.clipboard.dangerouslyPasteHTML(range.index, html);
        } else {
            // If no selection, insert at end
            quill.clipboard.dangerouslyPasteHTML(quill.getLength() - 1, html);
        }

        return true;
    }

    return false;
};

// Helper function to toggle auto-save for a Quill editor
window.quillToggleAutoSave = function(editorId, enable) {
    if (window.quillInstances && window.quillInstances[editorId]) {
        const quill = window.quillInstances[editorId];
        if (typeof quill.toggleAutoSave === 'function') {
            return quill.toggleAutoSave(enable);
        }
    }
    return false;
};

// Helper function to manually save a Quill editor's content
window.quillSaveContent = function(editorId) {
    if (window.quillInstances && window.quillInstances[editorId]) {
        const quill = window.quillInstances[editorId];
        if (typeof quill.save === 'function') {
            quill.save();
            return true;
        }
    }
    return false;
};