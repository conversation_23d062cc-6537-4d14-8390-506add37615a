import { Editor } from '@tiptap/core';
import StarterKit from '@tiptap/starter-kit';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import Underline from '@tiptap/extension-underline';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';

// Make editor available globally
window.setupTipTapEditor = function(element, content, updateCallback) {
    // Ensure content is a string
    const safeContent = content || '';

    return new Editor({
        element: element,
        content: safeContent,
        extensions: [
            StarterKit,
            TextAlign.configure({
                types: ['heading', 'paragraph'],
                alignments: ['left', 'center', 'right', 'justify'],
                defaultAlignment: 'left',
            }),
            TextStyle,
            FontFamily.configure({
                types: ['textStyle'],
                defaultFontFamily: 'Times New Roman',
            }),
            Underline,
            Table.configure({
                resizable: true,
            }),
            TableRow,
            TableCell,
            TableHeader,
        ],
        onUpdate: ({ editor }) => {
            if (typeof updateCallback === 'function') {
                updateCallback(editor.getHTML());
            }
        },
        editorProps: {
            attributes: {
                class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-xl focus:outline-none min-h-[500px] max-w-none',
            },
        },
    });
};

// Helper function to create a table
window.insertTable = function(editor) {
    editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
};

// Helper function to add a table row
window.addTableRow = function(editor) {
    editor.chain().focus().addRowAfter().run();
};

// Helper function to add a table column
window.addTableColumn = function(editor) {
    editor.chain().focus().addColumnAfter().run();
};

// Helper function to delete a table row
window.deleteTableRow = function(editor) {
    editor.chain().focus().deleteRow().run();
};

// Helper function to delete a table column
window.deleteTableColumn = function(editor) {
    editor.chain().focus().deleteColumn().run();
};

// Helper function to delete a table
window.deleteTable = function(editor) {
    editor.chain().focus().deleteTable().run();
};

// Helper function to toggle a heading
window.toggleHeading = function(editor, level) {
    editor.chain().focus().toggleHeading({ level: level }).run();
};

// Helper function to toggle bold
window.toggleBold = function(editor) {
    editor.chain().focus().toggleBold().run();
};

// Helper function to toggle italic
window.toggleItalic = function(editor) {
    editor.chain().focus().toggleItalic().run();
};

// Helper function to toggle underline
window.toggleUnderline = function(editor) {
    editor.chain().focus().toggleUnderline().run();
};

// Helper function to toggle a bullet list
window.toggleBulletList = function(editor) {
    editor.chain().focus().toggleBulletList().run();
};

// Helper function to toggle a ordered list
window.toggleOrderedList = function(editor) {
    editor.chain().focus().toggleOrderedList().run();
};

// Helper function to set text align
window.setTextAlign = function(editor, align) {
    editor.chain().focus().setTextAlign(align).run();
};

// Helper function to set font family
window.setFontFamily = function(editor, fontFamily) {
    editor.chain().focus().setFontFamily(fontFamily).run();
};

// Helper function to undo
window.undo = function(editor) {
    editor.chain().focus().undo().run();
};

// Helper function to redo
window.redo = function(editor) {
    editor.chain().focus().redo().run();
};
