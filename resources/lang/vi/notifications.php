<?php

return [
    'title' => 'Thông báo',
    'mark_all_read' => '<PERSON><PERSON><PERSON> dấu tất cả đã đọc',
    'mark_read' => 'Đánh dấu đã đọc',
    'no_notifications' => 'Không có thông báo mới',
    'view_case' => '<PERSON><PERSON> sơ',
    'new_notification' => 'Bạn đã nhận được thông báo mới',
    'invite_received' => 'Bạn đã được mời hợp tác trong hồ sơ ":case" với vai trò :role',
    'access_revoked' => 'Quyền truy cập của bạn vào hồ sơ ":case" đã bị thu hồi',
    'role_changed' => 'Vai trò của bạn trong hồ sơ ":case" đã được thay đổi thành :role',
    'contacts_processed' => ':count liên hệ đã được xử lý thành công',
    'contacts_processing_failed' => 'Xử lý danh bạ thất bại: :message',
    'research_items_generated' => ':count mục nghiên cứu đã được tạo',
    'research_initiated' => 'Khởi tạo nghiên cứu cho ":title"',
    'research_retry_initiated' => 'Khởi động lại nghiên cứu cho ":title"',
    'research_report_removed' => 'Báo cáo nghiên cứu đã bị xóa khỏi cơ sở kiến thức AI',
    'research_report_removal_failed' => 'Xóa báo cáo nghiên cứu khỏi cơ sở kiến thức AI thất bại',
    'research_already_in_progress' => 'Nghiên cứu đã đang diễn ra cho ":title"',
    'research_only_failed_retry' => 'Chỉ có thể thử lại nghiên cứu thất bại',
    'no_research_report' => 'Không có báo cáo nghiên cứu nào',
    'no_markdown_content' => 'Không có nội dung markdown',
    'lawyer_review_thanks' => 'Cảm ơn bạn đã quan tâm. Một luật sư có thể liên hệ với bạn sau khi bạn hoàn thành toàn bộ quá trình phỏng vấn.',
    'party_added' => ':name đã được thêm vào các bên trong vụ án',
    'invitation_sent' => 'Lời mời đã được gửi thành công.',
    'permissions_updated' => 'Quyền đã được cập nhật thành công.',
    'permissions_update_failed' => 'Không thể cập nhật quyền.',
    'error' => 'Lỗi: :message'
];
