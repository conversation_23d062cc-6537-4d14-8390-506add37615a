<?php

return [
    'title' => 'Pozycje badań prawnych',
    'view_thread' => 'Zobacz wątek badawczy',
    'generate_items' => 'Generuj pozycje badawcze',
    'generate_more' => 'Generuj więcej pozycji',
    'generate_more_title' => 'To spowoduje wygenerowanie dodatkowych pozycji badawczych',
    'processing' => 'Przetwarzanie...',
    'no_items' => 'Na razie nie wygenerowano żadnych pozycji badawczych. Kliknij przycisk powyżej, aby wygenerować pozycje na podstawie streszczenia sprawy.',
    'no_items_title' => 'Brak pozycji badawczych',
    'mark_active' => 'Oznacz jako aktywne',
    'mark_completed' => 'Oznacz jako zakończone',
    'archive' => 'Archiwum',
    'document_type' => 'Dokument',
    'error_message' => 'Błąd: :message',
    'no_case_summary' => 'Brak podsumowania sprawy do wygenerowania elementu badawczego',
    'status' => [
        'active' => 'Aktywny',
        'completed' => 'Zakończony',
        'archived' => 'Zarchiwizowany'
    ],
    'source_type' => [
        'case_law' => 'Prawo precedensowe',
        'statute' => 'Ustawa',
        'regulation' => 'Rozporządzenie',
        'legal_article' => 'Artykuł prawny',
        'legal_document' => 'Dokument prawny',
        'other' => 'Inne'
    ],
    'research_status' => [
        'not_started' => 'Nie rozpoczęto',
        'queued' => 'Badanie w kolejce...',
        'in_progress' => 'Badanie w toku...',
        'completed' => 'Zakończono',
        'failed' => 'Nie powiodło się'
    ],
    'actions' => [
        'start_research' => 'Rozpocznij badanie',
        'view_report' => 'Zobacz raport',
        'view_summary' => 'Zobacz podsumowanie',
        'research_again' => 'Badanie ponownie',
        'research_again_title' => 'Rozpocznij nowe badanie',
        'retry_research' => 'Spróbuj ponownie'
    ],
    'metadata' => [
        'citation' => 'Cytat',
        'additional_info' => 'Dodatkowe informacje',
        'field' => ':field'
    ],
    'ai_knowledge' => 'Wiedza AI',
    'remove_from_ai' => 'Usuń z bazy wiedzy AI',
    'relevance' => 'Znaczenie'
];
