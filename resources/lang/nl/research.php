<?php

return [
    'title' => 'Juridische Onderzoek Items',
    'view_thread' => 'Bekijk Onderzoeksdraad',
    'generate_items' => 'Genereer Onderzoek Items',
    'generate_more' => 'Genereer Meer Items',
    'generate_more_title' => 'Dit zal extra onderzoeksitems genereren',
    'processing' => 'Bezig met verwerken...',
    'no_items' => 'Er zijn nog geen onderzoeksitems gegenereerd. Klik op de knop hierboven om onderzoeksitems te genereren op basis van de casus samenvatting.',
    'no_items_title' => 'Nog geen onderzoeksitems',
    'mark_active' => 'Markeer als Actief',
    'mark_completed' => 'Markeer als Voltooid',
    'archive' => 'Archief',
    'document_type' => 'Document',
    'error_message' => 'Fout: :message',
    'no_case_summary' => 'Geen samenvatting beschikbaar voor het genereren van onderzoeksitems',
    'status' => [
        'active' => 'Actief',
        'completed' => 'Voltooid',
        'archived' => 'Gearchiveerd'
    ],
    'source_type' => [
        'case_law' => 'Precedent',
        'statute' => 'Wetsartikel',
        'regulation' => 'Verordening',
        'legal_article' => 'Juridisch artikel',
        'legal_document' => 'Juridisch document',
        'other' => 'Anders'
    ],
    'research_status' => [
        'not_started' => 'Nog niet begonnen',
        'queued' => 'Onderzoek in wachtrij...',
        'in_progress' => 'Onderzoek bezig...',
        'completed' => 'Voltooid',
        'failed' => 'Mislukt'
    ],
    'actions' => [
        'start_research' => 'Start onderzoek',
        'view_report' => 'Bekijk rapport',
        'view_summary' => 'Bekijk Samenvatting',
        'research_again' => 'Onderzoek Opnieuw',
        'research_again_title' => 'Start een nieuw onderzoek',
        'retry_research' => 'Onderzoek Opnieuw Proberen'
    ],
    'metadata' => [
        'citation' => 'Bronvermelding',
        'additional_info' => 'Aanvullende Informatie',
        'field' => ':veld'
    ],
    'ai_knowledge' => 'AI Kennis',
    'remove_from_ai' => 'Verwijder uit AI kennisbank',
    'relevance' => 'Relevantie'
];
