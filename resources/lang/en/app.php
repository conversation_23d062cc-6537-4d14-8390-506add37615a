<?php

return [
    'new_case' => 'New Case',
    'create_new_case' => 'Create New Case',
    'start_new_case' => 'Start A New Case File',
    'search_cases' => 'Search cases...',
    'assistant_ready' => 'Assistant Ready',
    'assistant_pending' => 'Assistant Pending',
    'delete_case_file' => 'Delete Case File?',
    'delete_confirmation' => 'Are you sure you want to delete this case file? This action cannot be undone.',
    'yes_delete_it' => 'Yes, delete it!',
    'cancel' => 'Cancel',
    'view_case' => 'View Case',
    'delete' => 'Delete',
    'collaborators' => 'Collaborators',
    'collaboration_enabled' => 'Collaboration Enabled',
    'view_all' => 'View All',
    'view' => 'View',
    'enter_case_number_if_available' => 'Enter case number if available',
    'create_and_continue' => 'Create & Continue',
    'creating' => 'Creating...',

    // Draft Documents
    'draft_documents' => 'Draft Documents',
    'go_to_draft_dashboard' => 'Go to Draft Dashboard',
    'document_editor' => 'Document Editor',
    'auto_save' => 'Auto-save',
    'save' => 'Save',
    'start_typing' => 'Start typing your document here...',
    'back_to_case' => 'Back to Case',
    'new_draft' => 'New Draft',
    'no_drafts_yet' => 'No drafts yet',
    'get_started_draft' => 'Get started by creating a new draft document.',
    'create_draft' => 'Create Draft',
    'type' => 'Type',
    'description' => 'Description',
    'status' => 'Status',
    'created' => 'Created',
    'no_description' => 'No description',
    'delete_draft_confirmation' => 'Are you sure you want to delete this draft?',

    // Caption Form Editor
    'caption_information' => 'Caption Information',
    'court_information' => 'Court Information',
    'court_name' => 'Court Name',
    'division' => 'Division',
    'case_number' => 'Case Number',
    'judge_name' => 'Judge Name',
    'plaintiffs' => 'Plaintiffs',
    'add_plaintiff' => 'Add Plaintiff',
    'name' => 'Name',
    'role' => 'Role',
    'plaintiff' => 'Plaintiff',
    'petitioner' => 'Petitioner',
    'defendants' => 'Defendants',
    'add_defendant' => 'Add Defendant',
    'defendant' => 'Defendant',
    'respondent' => 'Respondent',
    'document_title' => 'Document Title',

    // Exhibit Sidebar
    'exhibits' => 'Exhibits',
    'add_exhibit' => 'Add Exhibit',
    'no_exhibits_yet' => 'No exhibits added yet',
    'exhibit' => 'Exhibit',
    'standard' => 'Standard',
    'detailed' => 'Detailed',
    'legal' => 'Legal',
    'parenthetical' => 'Parenthetical',
    'add_new_exhibit' => 'Add New Exhibit',

    // AI Document Editor
    'editor' => 'Editor',
    'last_saved_at' => 'Last saved at',
    'back_to_drafts' => 'Back to Drafts',
    'ai_is_thinking' => 'AI is thinking',
    'ai_assistant' => 'AI Assistant',
    'ai_welcome_message' => 'Hello! I\'m your AI assistant for drafting legal documents. I can help you:',
    'generate_content_sections' => 'Generate content for specific sections',
    'create_complete_draft' => 'Create a complete document draft',
    'answer_legal_questions' => 'Answer questions about legal drafting',
    'provide_suggestions' => 'Provide suggestions for improving your document',
    'what_work_today' => 'What would you like to work on today?',
    'you' => 'You',
    'thinking' => 'Thinking',
    'generate_full_document' => 'Generate Full Document',
    'generate_current_section' => 'Generate Current Section',
    'hide_exhibits' => 'Hide Exhibits',
    'show_exhibits' => 'Show Exhibits',
    'test_insert_text' => 'Test Insert Text',
    'ask_help_document' => 'Ask me to help with your document...',
    'thinking_ellipsis' => 'Thinking...',
    'enter_shift_newline' => 'Press Enter to send, Shift+Enter for new line',
    'save_section' => 'Save Section',
    'debug' => 'Debug:',
    'content_length' => 'Content length:',
    'bytes' => 'bytes',
    'save_caption' => 'Save Caption',
    'ai_suggestions' => 'AI Suggestions',
    'apply_to_section' => 'Apply to Section',
    'insert' => 'Insert',
    'dismiss' => 'Dismiss',
    'select_section_edit' => 'Select a section to edit',
    'click_section_start' => 'Click on a section name above to start editing',

    // Draft Creation
    'create_new_draft' => 'Create New Draft',
    'use_template' => 'Use a Template',
    'select_template_quick' => 'Select a template to quickly create a pre-structured document.',
    'use_template_btn' => 'Use Template',
    'no_templates' => 'No templates available. Create a template first.',
    'create_template' => 'Create Template',
    'create_from_scratch' => 'Create from Scratch',
    'create_blank_document' => 'Create a new blank document without using a template.',
    'document_type' => 'Document Type',
    'select_document_type' => 'Select document type',
    'complaint' => 'Complaint',
    'pleading' => 'Pleading',
    'motion' => 'Motion',
    'affidavit' => 'Affidavit',
    'proposed_order' => 'Proposed Order',
    'certificate_of_service' => 'Certificate of Service',
    'letter' => 'Letter',
    'contract' => 'Contract',
    'brief_description' => 'Brief description of this document',

    // Draft Details and Show
    'draft' => 'Draft',
    'draft_details' => 'Draft Details',
    'back_to_draft' => 'Back to Draft',
    'edit_draft' => 'Edit Draft',
    'update_draft' => 'Update Draft',
    'delete_draft' => 'Delete Draft',
    'open_editor' => 'Open Editor',
    'open_in_editor' => 'Open in Editor',
    'no_description_provided' => 'No description provided',
    'last_updated' => 'Last Updated',
    'published' => 'Published',
    'document_metadata' => 'Document Metadata',
    'sections' => 'Sections',
    'structure' => 'Structure',
    'metadata' => 'Metadata',
    'order' => 'Order',
    'section' => 'Section',
    'required' => 'Required',
    'optional' => 'Optional',
    'content_preview' => 'Content Preview',
    'no_content' => 'No content',
    'no_sections_defined' => 'No sections defined',
    'no_structure_defined' => 'No structure defined',
    'no_metadata_defined' => 'No metadata defined',
    'actions' => 'Actions',
    'edit_properties' => 'Edit Properties',
    'generate_document' => 'Download Document',
    'delete_draft_confirm' => 'Are you sure you want to delete this draft?',
    'document_content' => 'Document Content',
    'basic_editor' => 'Basic Editor',
    'ai_powered_editor' => 'AI-Powered Editor',
    'download' => 'Download',
    'review' => 'Review',
    'please_wait' => 'Please Wait',
    'processing_your_request' => 'Processing your request...',
    'no_suggestions_available' => 'No suggestions available',
];
