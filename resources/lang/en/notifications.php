<?php

return [
    // UI Elements
    'title' => 'Notifications',
    'mark_all_read' => 'Mark all as read',
    'mark_read' => 'Mark as read',
    'no_notifications' => 'No new notifications',
    'view_case' => 'View Case',
    'new_notification' => 'New notification received',

    // Collaboration Notifications
    'invite_received' => 'You have been invited to collaborate on case ":case" as :role',
    'access_revoked' => 'Your access to case ":case" has been revoked',
    'role_changed' => 'Your role for case ":case" has been changed to :role',

    // Address Book Notifications
    'contacts_processed' => ':count contacts processed successfully',
    'contacts_processing_failed' => 'Failed to process contacts: :message',

    // Research Notifications
    'research_items_generated' => ':count research items generated',
    'research_initiated' => 'Research initiated for ":title"',
    'research_retry_initiated' => 'Research retry initiated for ":title"',
    'research_report_removed' => 'Research report removed from AI knowledge base',
    'research_report_removal_failed' => 'Failed to remove research report from AI knowledge base',
    'research_already_in_progress' => 'Research is already in progress for ":title"',
    'research_only_failed_retry' => 'Only failed research can be retried',
    'no_research_report' => 'No research report available',
    'no_markdown_content' => 'No markdown content available',
    'research_item_deleted' => 'Research item ":title" has been deleted successfully',
    'research_item_created' => 'Research item ":title" has been created successfully',

    // Interview Notifications
    'lawyer_review_thanks' => 'Thank you for your interest. A lawyer may contact you after you complete the entire interview process.',
    'party_added' => ':name added to case parties',

    // Invitation Notifications
    'invitation_sent' => 'Invitation sent successfully.',

    // Permissions Notifications
    'permissions_updated' => 'Permissions updated successfully.',
    'permissions_update_failed' => 'Failed to update permissions.',

    // Error Notifications
    'error' => 'Error: :message'
];