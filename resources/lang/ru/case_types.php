<?php

return [
    'Contract Law' => 'Закон о договорах: регулирует соглашения между двумя или более сторонами, включая их заключение, толкование и исполнение.',
    'Tort Law' => 'Гражданское право: занимается неправомерными действиями, наносящими вред или ущерб другому лицу, что может привести к ответственности.',
    'Property Law' => 'Право собственности: касается прав и обязанностей, связанных с владением, использованием и передачей имущества.',
    'Family Law' => 'Семейное право: регулирует юридические отношения внутри семьи, включая брак, развод, опеку и усыновление.',
    'Criminal Law' => 'Уголовное право: занимается преступлениями, считающимися нарушениями общественного порядка, наказываемыми штрафами или лишением свободы.',
    'Employment Law' => 'Трудовое право: регулирует отношения между работодателями и работниками.',
    'Consumer Law' => 'Права потребителей: защищает лиц, приобретающих товары и услуги, от недобросовестных или вводящих в заблуждение практик.',
    'Constitutional Law' => 'Конституционное право: интерпретирует и применяет Конституцию, определяет структуру правительства и защищает права граждан.',
    'Administrative Law' => 'Административное право: регулирует действия и решения государственных административных органов.',
    'Environmental Law' => 'Экологическое право: регулирует взаимодействие человека с окружающей средой для защиты природных ресурсов.',
    'Immigration Law' => 'Законодательство, регулирующее права иностранных граждан при въезде и проживании в стране.',
    'Estate Planning' => 'Управление и распределение активов после смерти, включая завещания и трасты.',
    'Bankruptcy Law' => 'Правовой процесс для физических и юридических лиц, неспособных погасить свои долги.',
    'Intellectual Property' => 'Защита интеллектуальных достижений, таких как изобретения, литературные произведения и бренды.'
];
