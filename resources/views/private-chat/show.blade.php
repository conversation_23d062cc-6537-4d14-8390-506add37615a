<x-app-layout>
    <div class="py-6 sm:py-12">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div class="bg-base-100/50 dark:bg-neutral-focus/50 backdrop-blur-xl shadow-xl sm:rounded-lg p-8">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 h-[calc(80vh-4rem)]">
                    <!-- Thread List Sidebar -->
                    <div class="hidden md:block md:col-span-1 border-r border-base-300">
                        <livewire:private-chat.private-thread-list :activeThreadId="$thread->id" />
                    </div>

                    <!-- Chat Interface -->
                    <div class="col-span-1 md:col-span-3 flex flex-col h-full chat-interface-wrapper overflow-hidden">
                        <livewire:private-chat.private-chat-interface :thread="$thread" />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Video Call Modal -->
{{--    <x-video-call-modal id="video-call-modal" />--}}

    <script>
        function startVideoCall(threadId) {
            // Create a room name based on the thread ID
            const roomPrefix = 'chat_';

            // Make an AJAX request to create the meeting
            fetch('{{ route("video-meetings.create") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    type: 'chat',
                    id: threadId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Open the meeting in a new tab
                    window.open(data.meeting_url, '_blank');
                } else {
                    console.error('Failed to create meeting:', data.error);
                    alert('{{ __('private-chat.failed_send') }}. {{ __('private-chat.try_again') }}');
                }
            })
            .catch(error => {
                console.error('Error creating meeting:', error);
                alert('{{ __('private-chat.failed_load') }}. {{ __('private-chat.try_again') }}');
            });
        }
    </script>
</x-app-layout>
