<div class="p-4 border-t border-base-content/10 dark:border-base-content/70 flex-shrink-0" style="position: sticky; bottom: 0; background-color: inherit; z-index: 10000; width: 100%;">
    <style>
        /* Ensure the input area is always visible */
        .p-4.border-t {
            position: sticky;
            bottom: 0;
            z-index: 10000 !important;
            background-color: inherit;
            width: 100% !important;
        }
    </style>
    @if ($currentThread)
        @if($insufficientCredits)
        <div class="alert alert-warning mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
            <div>
                <h3 class="font-bold">Insufficient Credits</h3>
                <div class="text-sm">You need at least {{ $requiredCredits }} credits to send messages to the AI assistant. Your current balance is {{ $currentBalance }} credits.</div>
                <a href="{{ route('credits.purchase.form') }}" class="btn btn-sm btn-outline mt-2">Purchase Credits</a>
            </div>
        </div>
        @endif
        <div class="flex flex-col p-3 space-y-2 rounded-lg shadow-lg bg-base-200 sm:p-4 sm:space-y-3"
            x-data="{
                uploading: false,
                recording: false,
                sending: false,
                mediaRecorder: null,
                audioChunks: [],
                allowedMimeTypes: ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/m4a', 'audio/mp4', 'audio/x-m4a', 'audio/webm', 'video/webm', 'audio/ogg'],
                maxFileSize: 25 * 1024 * 1024, // 25MB in bytes
                queuedFiles: [],

                // Add responsive state
                isMobile: window.innerWidth < 640,

                // Initialize responsive detection and wire up files to Livewire
                init() {
                    this.checkMobile();
                    window.addEventListener('resize', () => this.checkMobile());
                },

                checkMobile() {
                    this.isMobile = window.innerWidth < 640;
                },

                // File handling methods
                handleFileUpload(event) {
                    const files = event.target.files;
                    if (!files.length) return;

                    Array.from(files).forEach(file => {
                        if (!this.validateFile(file)) return;

                        // Create a FormData object to upload the file
                        const formData = new FormData();
                        formData.append('tempUpload', file);

                        // Upload the file to Livewire
                        $wire.upload('tempUpload', file, (uploadedFilename) => {
                            // On success, call the uploadFile method
                            $wire.uploadFile().then(result => {
                                if (result.success) {
                                    // Add to queued files with the index from Livewire
                                    this.queuedFiles.push({
                                        file: file,
                                        name: file.name,
                                        size: this.formatFileSize(file.size),
                                        type: file.type,
                                        index: result.index
                                    });

                                    // Update Livewire's queuedFiles for display
                                    $wire.set('queuedFiles', this.queuedFiles.map(item => ({
                                        name: item.name,
                                        size: item.size,
                                        type: item.type,
                                        index: item.index
                                    })));
                                }
                            });
                        });
                    });

                    // Reset file input
                    event.target.value = '';
                },

                removeQueuedFile(index) {
                    // Find the item with this index
                    const itemIndex = this.queuedFiles.findIndex(item => item.index === index);
                    if (itemIndex !== -1) {
                        // Remove from Alpine.js array
                        this.queuedFiles.splice(itemIndex, 1);
                        // Tell Livewire to remove the file
                        $wire.removeQueuedFile(index);
                        // Update Livewire's queuedFiles for display
                        $wire.set('queuedFiles', this.queuedFiles.map(item => ({
                            name: item.name,
                            size: item.size,
                            type: item.type,
                            index: item.index
                        })));
                    }
                },

                formatFileSize(bytes) {
                    if (bytes < 1024) return bytes + ' B';
                    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
                    else return (bytes / 1048576).toFixed(1) + ' MB';
                },

                validateFile(file) {
                    // Add your file validation logic here
                    // For now, we'll just check size
                    if (file.size > this.maxFileSize) {
                        alert(`File size (${(file.size / (1024 * 1024)).toFixed(2)}MB) exceeds maximum allowed size of ${this.maxFileSize / (1024 * 1024)}MB`);
                        return false;
                    }
                    return true;
                },

                getSupportedMimeType() {
                    const types = [
                        'audio/webm;codecs=opus',
                        'audio/webm',
                        'audio/wav',
                        'audio/mp3',
                        'audio/mpeg',
                        'audio/m4a',
                        'audio/mp4',
                        'audio/x-m4a',
                        'audio/ogg'
                    ];

                    for (const type of types) {
                        if (MediaRecorder.isTypeSupported(type)) {
                            return type;
                        }
                    }
                    throw new Error('No supported audio MIME type found in this browser');
                },

                async startRecording() {
                    try {
                        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                        const mimeType = this.getSupportedMimeType();

                        this.mediaRecorder = new MediaRecorder(stream, {
                            mimeType: mimeType,
                            audioBitsPerSecond: 128000
                        });

                        this.audioChunks = [];

                        this.mediaRecorder.ondataavailable = (event) => {
                            if (event.data.size > 0) {
                                this.audioChunks.push(event.data);
                            }
                        };

                        this.mediaRecorder.onstop = async () => {
                            const audioBlob = new Blob(this.audioChunks, { type: mimeType });
                            await this.uploadAudioBlob(audioBlob);
                            stream.getTracks().forEach(track => track.stop());
                        };

                        this.mediaRecorder.start(1000); // Collect data every second
                        this.recording = true;
                    } catch (error) {
                        console.error('Recording error:', error);
                        alert('Error accessing microphone: ' + (error.message || 'Unknown error occurred'));
                    }
                },

                stopRecording() {
                    if (this.mediaRecorder && this.recording) {
                        this.mediaRecorder.stop();
                        this.recording = false;
                    }
                },

                async uploadAudioBlob(blob) {
                    if (blob.size > this.maxFileSize) {
                        alert(`Recording size (${(blob.size / (1024 * 1024)).toFixed(2)}MB) exceeds maximum allowed size of ${this.maxFileSize / (1024 * 1024)}MB`);
                        return;
                    }

                    this.uploading = true;
                    const formData = new FormData();
                    const extension = blob.type.includes('webm') ? 'webm' : 'wav';
                    formData.append('audio', blob, `recording.${extension}`);

                    try {
                        console.log('Uploading audio blob:', {
                            size: (blob.size / 1024).toFixed(2) + 'KB',
                            type: blob.type,
                            filename: `recording.${extension}`
                        });

                        const response = await fetch('/transcribe', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content,
                                'Accept': 'application/json'
                            },
                            credentials: 'same-origin'
                        });

                        console.log('Response status:', response.status);
                        const data = await response.json();
                        console.log('Response data:', data);

                        if (data.success) {
                            $wire.appendTranscription(data.transcription);
                            this.uploading = false;
                        } else {
                            throw new Error(data.error || 'Unknown error occurred');
                        }
                    } catch (error) {
                        console.error('Error details:', {
                            message: error.message,
                            stack: error.stack,
                            name: error.name
                        });
                        alert('Error uploading recording: ' + error.message);
                    } finally {
                        this.uploading = false;
                    }
                },

                uploadAudio(event) {
                    const file = event.target.files[0];
                    if (!file) {
                        console.log('No file selected');
                        return;
                    }

                    if (!this.allowedMimeTypes.includes(file.type)) {
                        alert('Invalid file type. Please upload an audio file.');
                        return;
                    }

                    if (file.size > this.maxFileSize) {
                        alert(`File size (${(file.size / (1024 * 1024)).toFixed(2)}MB) exceeds maximum allowed size of ${this.maxFileSize / (1024 * 1024)}MB`);
                        return;
                    }

                    this.uploading = true;
                    const formData = new FormData();
                    formData.append('audio', file);

                    fetch('/transcribe', {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content,
                                'Accept': 'application/json'
                            },
                            credentials: 'same-origin'
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                $wire.appendTranscription(data.transcription);
                                // Update the local message variable to match what's in the textarea
                                this.message = $wire.message;
                            } else {
                                throw new Error(data.error || 'Unknown error occurred');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('Error uploading file: ' + error.message);
                        })
                        .finally(() => {
                            this.uploading = false;
                            event.target.value = '';
                        });
                }
            }">
            <style>
                @keyframes pulse {

                    0%,
                    100% {
                        opacity: 1;
                    }

                    50% {
                        opacity: 0.5;
                    }
                }

                /* Mobile optimizations */
                @media (max-width: 640px) {
                    .button-text {
                        display: none;
                    }

                    .mobile-btn-group {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 0.5rem;
                        justify-content: flex-start;
                        width: 100%;
                    }

                    .mobile-btn-group button {
                        flex: 0 0 auto;
                        padding: 0.5rem;
                        min-width: 2.5rem;
                    }

                    /* Ensure the button container has proper spacing */
                    .button-container {
                        display: flex;
                        flex-direction: column;
                        width: 100%;
                    }

                    /* Make send button full width on mobile */
                    .send-button-container {
                        margin-top: 0.5rem;
                        width: 100%;
                    }

                    .send-button-container button {
                        width: 100%;
                    }
                }
            </style>

            <!-- Document Preview Area -->
            <div class="mb-2" x-show="$wire.selectedDocuments.length > 0"
                 x-init='
                    window.addEventListener("reset-document-selection", () => {
                        // Reset all document checkboxes in the modal
                        document.querySelectorAll(".document-checkbox").forEach(checkbox => {
                            checkbox.checked = false;
                        });
                    });

                    window.addEventListener("unset-checkbox-update", (res) => {
                            let documentId = res.detail.documentId;
                            const checkbox = document.querySelector(`input[data-document-id="${documentId}"]`);
                            if (checkbox) {
                               checkbox.checked = !checkbox.checked;
                            }
                        });






                 '>
                <div class="mb-1 text-sm font-medium">{{ __('voice.selected_documents') }}</div>
                <div class="flex flex-wrap gap-2">
                    @foreach($selectedDocuments as $docId)
                        @php
                            $document = \App\Models\Document::find($docId);
                        @endphp
                        @if($document)
                        <div class="inline-flex items-center px-2 py-1 text-xs rounded-full bg-base-300">
                            <span class="mr-1 max-w-[150px] truncate">{{ $document->title }}</span>
                            <button type="button" wire:click="toggleDocument({{ $docId }})"
                                class="text-error hover:text-error-focus">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        @endif
                    @endforeach
                </div>
            </div>

            {{-- Text Area Container --}}
            <div class="relative">
                <textarea wire:model.live.debounce.300ms="message" name="{{ $name }}"
                    class="w-full bg-white textarea textarea-bordered text-base-content placeholder-base-content/50"
                    style="height: {{ $height }};" placeholder="{{ $placeholder ?? __('chat.message_placeholder') }}"></textarea>

                {{-- Loading Indicator --}}
                <div x-show="uploading || recording"
                    class="absolute inset-0 flex items-center justify-center rounded-lg bg-base-100/50">
                    <div class="flex flex-col items-center">
                        <div class="loading loading-spinner loading-lg text-primary"></div>
                        <span x-text="recording ? '{{ __('chat.recording') }}' : '{{ __('chat.transcribing') }}'"
                            class="mt-2 text-sm font-medium animate-pulse"
                            style="animation: pulse 1.5s infinite;"></span>
                    </div>
                </div>
            </div>

            {{-- Button Group --}}
            <div class="flex items-center justify-between mt-2 button-container">
                {{-- Left side: Action Buttons --}}
                <div class="flex items-center space-x-2 mobile-btn-group">
                    {{-- Voice Recorder Button --}}
                    <button type="button" @click="recording ? stopRecording() : startRecording()"
                        :class="{ 'btn-error': recording }" class="text-white btn btn-sm btn-secondary">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                        <span class="ml-2 button-text"
                            x-text="recording ? '{{ __('voice.stop_recording') }}' : '{{ __('voice.start_recording') }}'"></span>
                    </button>

                    {{-- Voice Upload Button --}}
{{--                    <input type="file" accept="audio/*,video/webm" class="hidden"--}}
{{--                        id="audio-upload-{{ $name }}" @change="uploadAudio($event)" />--}}

{{--                    <button type="button" @click="document.getElementById('audio-upload-{{ $name }}').click()"--}}
{{--                        :disabled="uploading || recording" class="text-white btn btn-sm btn-secondary">--}}
{{--                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"--}}
{{--                            stroke="currentColor">--}}
{{--                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"--}}
{{--                                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />--}}
{{--                        </svg>--}}
{{--                        <span class="ml-2 button-text"--}}
{{--                            x-text="uploading ? '{{ __('chat.transcribing') }}' : '{{ __('chat.voice_note') }}'"></span>--}}
{{--                    </button>--}}

                    {{-- Document Attachment Button --}}
{{--                    <button type="button" wire:click="loadAvailableDocuments"--}}
{{--                        :disabled="uploading || recording" class="text-white btn btn-sm btn-secondary">--}}
{{--                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"--}}
{{--                            stroke="currentColor">--}}
{{--                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"--}}
{{--                                d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />--}}
{{--                        </svg>--}}
{{--                        <span class="ml-2 button-text">{{ __('chat.attach_documents') }}</span>--}}
{{--                    </button>--}}

                </div>

                {{-- Right side: Send Button (Configurable) --}}

                <div class="send-button-container">
                    <button type="button" wire:click="sendMessage()" @click="scrollToVeryBottom(); $dispatch('messages-updated')"
                        :disabled="uploading || recording || sending || (!$wire.message && queuedFiles.length === 0) || {{ $insufficientCredits ? 'true' : 'false' }}"
                        class="text-white btn btn-sm btn-primary">
                        <div class="flex items-center">
                            <span x-show="!sending && !{{ $insufficientCredits ? 'true' : 'false' }}" class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20"
                                    fill="currentColor">
                                    <path
                                        d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                                </svg>
                                <span class="ml-2 button-text">{{ $sendButtonText ?? __('chat.send') }}</span>
                            </span>
                            <span x-show="sending" class="flex items-center">
                                <div class="loading loading-spinner loading-xs"></div>
                                <span class="ml-2 button-text">{{ __('chat.sending') }}</span>
                            </span>
                            <span x-show="{{ $insufficientCredits ? 'true' : 'false' }} && !sending" class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <span class="ml-2 button-text">Need Credits</span>
                            </span>
                        </div>
                    </button>
                </div>

            </div>
        </div>
    @endif
</div>

{{-- Add Document Selection Modal --}}
<div x-data="{ open: @entangle('showDocumentModal') }"
     x-show="open"
     class="fixed inset-0 z-50 overflow-y-auto"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:align-middle sm:max-w-lg sm:w-full dark:bg-gray-800">
            <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="w-full mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg font-medium leading-6 text-gray-900 dark:text-white">
                            {{ __('chat.select_documents') }}
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                {{ __('chat.select_documents_description') }}
                            </p>
                            <div class="mt-4 space-y-2 max-h-96 overflow-y-auto">
                                @if(count($availableDocuments) > 0)
                                    @foreach($availableDocuments as $document)

                                        <div class="flex items-center p-2 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 dark:border-gray-700">
                                            <input type="checkbox"
                                                   wire:click="toggleDocument({{ $document->id }})"
                                                   data-document-id="{{ $document->id }}"
                                                   class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 document-checkbox"
                                                   {{ in_array($document->id, $selectedDocuments) ? 'checked' : '' }}>
                                            <label class="block ml-2 text-sm font-medium text-gray-900 dark:text-gray-200">
                                                {{ $document->title }}
                                                <span class="text-xs text-gray-500 dark:text-gray-400">
                                                    ({{ $document->mime_type  }})
                                                </span>
                                            </label>
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-sm text-gray-500 dark:text-gray-400">{{ __('voice.no_documents') }}</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" wire:click="closeDocumentModal"
                       class="inline-flex justify-center w-full px-4 py-2 text-base font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {{ __('voice.done') }}
                </button>
                <button type="button" wire:click="closeDocumentModal"
                       class="inline-flex justify-center w-full px-4 py-2 mt-3 text-base font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-gray-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-700">
                    {{ __('voice.cancel') }}
                </button>
            </div>
        </div>
    </div>
</div>
