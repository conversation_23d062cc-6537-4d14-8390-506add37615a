<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100/50 dark:bg-neutral-focus/50 backdrop-blur-xl shadow-xl sm:rounded-lg p-8">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold">{{ __('Purchase Credits') }}</h2>
                    <p class="mt-2 text-base-content/70">
                        Select a credit package and complete your purchase to add credits to your account.
                    </p>
                    <div class="mt-4 flex flex-wrap gap-4">
                        <div class="alert alert-info py-2 px-4 inline-flex max-w-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                            <span>Looking for a monthly subscription? <a href="{{ route('subscriptions.index') }}" class="link link-primary font-semibold">View subscription plans</a></span>
                        </div>
                    </div>
                </div>

                <!-- Credit Packages -->
                <div class="mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold">One-time Credit Packages</h3>
                        <a href="{{ route('subscriptions.index') }}" class="btn btn-outline btn-sm">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            View Subscriptions
                        </a>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        @foreach($products as $product)
                            <div class="card bg-base-200 shadow-md hover:shadow-lg transition-shadow cursor-pointer"
                                 onclick="selectProduct({{ $product->id }}, '{{ $product->name }}', {{ $product->credits }}, {{ $product->price_cents }})">
                                <div class="card-body">
                                    <h4 class="card-title">{{ $product->name }}</h4>
                                    <p class="text-3xl font-bold text-primary">{{ number_format($product->credits) }}</p>
                                    <p class="text-base-content/70">credits</p>
                                    <div class="mt-4">
                                        <p class="text-xl font-semibold">{{ $product->formatted_price }}</p>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>

                <!-- Comparison Section -->
                <div class="mb-8">
                    <div class="divider">OR</div>

                    <div class="card bg-base-100 shadow-md p-6">
                        <h3 class="text-xl font-semibold mb-4">One-time Purchase vs. Subscription</h3>

                        <div class="overflow-x-auto">
                            <table class="table w-full">
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th>One-time Purchase</th>
                                        <th>Monthly Subscription</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="font-medium">Credits</td>
                                        <td>Pay for what you need</td>
                                        <td>Regular monthly credits</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium">Cost</td>
                                        <td>Higher per-credit cost</td>
                                        <td>Lower per-credit cost</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium">Best for</td>
                                        <td>Occasional use</td>
                                        <td>Regular use</td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium">Additional benefits</td>
                                        <td>No commitment</td>
                                        <td>Extra features based on plan</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-6 text-center">
                            <a href="{{ route('subscriptions.index') }}" class="btn btn-primary">
                                View Subscription Plans
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <div id="payment-form-container" class="hidden">
                    <h3 class="text-xl font-semibold mb-4">Payment Details</h3>

                    <div class="card bg-base-200 shadow-md p-6">
                        <div class="mb-4">
                            <p class="text-lg font-medium">Selected Package: <span id="selected-package-name"></span></p>
                            <p class="text-lg font-medium">Credits: <span id="selected-package-credits"></span></p>
                            <p class="text-lg font-medium">Price: <span id="selected-package-price"></span></p>
                        </div>

                        <form id="payment-form" action="{{ route('credits.purchase') }}" method="POST">
                            @csrf
                            <input type="hidden" name="product_id" id="product-id">

                            <div class="mb-4">
                                <label for="card-element" class="block text-sm font-medium mb-2">
                                    Credit or debit card
                                </label>
                                <div id="card-element" class="p-3 border rounded-md bg-base-100">
                                    <!-- Stripe Card Element will be inserted here -->
                                </div>
                                <div id="card-errors" class="text-error text-sm mt-2" role="alert"></div>
                            </div>

                            <div class="flex justify-end mt-6">
                                <button type="button" onclick="cancelPayment()" class="btn btn-ghost mr-2">
                                    Cancel
                                </button>
                                <button type="submit" id="submit-button" class="btn btn-primary">
                                    Complete Purchase
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        // Initialize Stripe
        const stripe = Stripe('{{ env('STRIPE_KEY') }}');
        const elements = stripe.elements();

        // Create card element
        const cardElement = elements.create('card');
        cardElement.mount('#card-element');

        // Handle form submission
        const form = document.getElementById('payment-form');
        const submitButton = document.getElementById('submit-button');

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            // Disable the submit button to prevent multiple submissions
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Processing...';

            try {
                // Create payment method
                const {paymentMethod, error} = await stripe.createPaymentMethod({
                    type: 'card',
                    card: cardElement,
                });

                if (error) {
                    // Show error to customer
                    const errorElement = document.getElementById('card-errors');
                    errorElement.textContent = error.message;

                    // Re-enable the submit button
                    submitButton.disabled = false;
                    submitButton.innerHTML = 'Complete Purchase';
                    return;
                }

                // Add payment method ID to form
                const hiddenInput = document.createElement('input');
                hiddenInput.setAttribute('type', 'hidden');
                hiddenInput.setAttribute('name', 'payment_method_id');
                hiddenInput.setAttribute('value', paymentMethod.id);
                form.appendChild(hiddenInput);

                // Submit the form
                form.submit();
            } catch (err) {
                console.error('Error:', err);
                const errorElement = document.getElementById('card-errors');
                errorElement.textContent = 'An unexpected error occurred. Please try again.';

                // Re-enable the submit button
                submitButton.disabled = false;
                submitButton.innerHTML = 'Complete Purchase';
            }
        });

        // Function to select a product
        function selectProduct(id, name, credits, priceCents) {
            document.getElementById('product-id').value = id;
            document.getElementById('selected-package-name').textContent = name;
            document.getElementById('selected-package-credits').textContent = credits.toLocaleString();
            document.getElementById('selected-package-price').textContent = '$' + (priceCents / 100).toFixed(2);

            document.getElementById('payment-form-container').classList.remove('hidden');

            // Scroll to payment form
            document.getElementById('payment-form-container').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Function to cancel payment
        function cancelPayment() {
            document.getElementById('payment-form-container').classList.add('hidden');
        }
    </script>
    @endpush
</x-app-layout>
