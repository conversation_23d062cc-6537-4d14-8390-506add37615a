<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100/50 dark:bg-neutral-focus/50 backdrop-blur-xl shadow-xl sm:rounded-lg p-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold">{{ __('Credits Dashboard') }}</h2>
                    <div class="flex space-x-4">
                        @if($hasActiveSubscription)
                            <a href="{{ route('credits.purchase.form') }}" class="btn btn-primary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" />
                                </svg>
                                Buy Credits
                            </a>
                        @else
                            <a href="{{ route('subscriptions.index') }}" class="btn btn-primary">
                                <span>View Plans</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        @endif
                    </div>
                </div>

                <!-- Credit Balance Card -->
                <div class="card bg-base-200 shadow-md mb-8">
                    <div class="card-body">
                        <h3 class="card-title text-xl">Your Credit Balance</h3>
                        <div class="flex items-center mt-2">
                            <div class="stat-value text-primary text-4xl">{{ number_format($balance) }}</div>
                            <div class="ml-2 text-base-content/70">credits</div>
                        </div>
                        <p class="mt-2 text-base-content/70">
                            Credits power Justice Quest's AI-driven legal assistance features. Each feature consumes credits based on its computational complexity.
                        </p>

                        @if($balance > 0)
                        <div class="mt-4 p-3 bg-success/10 rounded-lg border border-success/20">
                            <h4 class="font-semibold text-success mb-2">What You Can Do Right Now:</h4>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                                @if($balance >= 300)
                                    <div class="text-center p-2 bg-success/20 rounded">
                                        <div class="font-bold">{{ floor($balance / 300) }}</div>
                                        <div>Deep Research</div>
                                    </div>
                                @endif
                                @if($balance >= 100)
                                    <div class="text-center p-2 bg-success/20 rounded">
                                        <div class="font-bold">{{ floor($balance / 100) }}</div>
                                        <div>New Cases</div>
                                    </div>
                                @endif
                                @if($balance >= 50)
                                    <div class="text-center p-2 bg-success/20 rounded">
                                        <div class="font-bold">{{ floor($balance / 50) }}</div>
                                        <div>Web Searches</div>
                                    </div>
                                @endif
                                @if($balance >= 10)
                                    <div class="text-center p-2 bg-success/20 rounded">
                                        <div class="font-bold">{{ floor($balance / 10) }}</div>
                                        <div>Documents</div>
                                    </div>
                                @endif
                                @if($balance >= 5)
                                    <div class="text-center p-2 bg-success/20 rounded">
                                        <div class="font-bold">{{ floor($balance / 5) }}</div>
                                        <div>AI Chats</div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        @elseif($balance == 0)
                        <div class="mt-4 p-3 bg-warning/10 rounded-lg border border-warning/20">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-warning mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                                <span class="text-sm font-medium text-warning">You're out of credits! Purchase more to continue using AI features.</span>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- How Credits Work -->
                <div class="mb-8">
                    <div class="card bg-base-200 shadow-md mb-4">
                        <div class="card-body">
                            <button type="button" onclick="toggleCreditInfo()" class="flex items-center justify-between w-full text-left text-lg font-medium hover:bg-base-300 p-2 rounded transition-colors">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                    How Credits Work
                                </div>
                                <svg id="credit-info-arrow" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transform transition-transform duration-200" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div id="credit-info-content" class="hidden mt-4">

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Case Management -->
                            <div>
                                <h4 class="font-semibold text-lg mb-3 text-primary">Case Management</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                        <span class="text-sm">Initializing a New Case</span>
                                        <span class="badge badge-primary">100 credits</span>
                                    </div>
                                    <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                        <span class="text-sm">Legal Research Report</span>
                                        <span class="badge badge-primary">100 credits</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Document Processing -->
                            <div>
                                <h4 class="font-semibold text-lg mb-3 text-primary">Document Processing</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                        <span class="text-sm">Document Analysis & Summary</span>
                                        <span class="badge badge-primary">10 credits</span>
                                    </div>
                                    <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                        <span class="text-sm">Audio Transcription (additional)</span>
                                        <span class="badge badge-primary">+5 credits</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Independent Research -->
                            <div>
                                <h4 class="font-semibold text-lg mb-3 text-primary">Independent Research</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                        <span class="text-sm">Short Web Search</span>
                                        <span class="badge badge-primary">50 credits</span>
                                    </div>
                                    <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                        <span class="text-sm">Regular Research Report</span>
                                        <span class="badge badge-primary">100 credits</span>
                                    </div>
                                    <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                        <span class="text-sm">Deep Research Report</span>
                                        <span class="badge badge-primary">300 credits</span>
                                    </div>
                                </div>
                            </div>

                            <!-- AI Assistant -->
                            <div>
                                <h4 class="font-semibold text-lg mb-3 text-primary">AI Assistant</h4>
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                        <span class="text-sm">Chat Interaction</span>
                                        <span class="badge badge-primary">5 credits</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 p-4 bg-info/10 rounded-lg border border-info/20">
                            <div class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-info mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                                <div>
                                    <h5 class="font-semibold text-info mb-1">Why Credits?</h5>
                                    <p class="text-sm text-base-content/80">
                                        Credits ensure fair usage of our AI-powered legal tools. Each feature requires different amounts of computational resources,
                                        so credits are priced accordingly. This system allows us to provide high-quality AI assistance while maintaining
                                        sustainable service costs.
                                    </p>
                                </div>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Transactions -->
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold">Recent Transactions</h3>
                        <a href="{{ route('credits.transactions') }}" class="text-primary hover:underline">View All</a>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Description</th>
                                    <th>Amount</th>
                                    <th>Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($transactions as $transaction)
                                    <tr>
                                        <td>{{ $transaction->created_at->format('M j, Y g:i A') }}</td>
                                        <td>{{ $transaction->description }}</td>
                                        <td class="{{ $transaction->amount > 0 ? 'text-success' : 'text-error' }}">
                                            {{ $transaction->amount > 0 ? '+' : '' }}{{ number_format($transaction->amount) }}
                                        </td>
                                        <td>{{ number_format($transaction->balance_after) }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="4" class="text-center py-4">No transactions yet.</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Purchase Options -->
                <div class="mb-8">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-semibold">Purchase Options</h3>
                        @if($hasActiveSubscription)
                            <div class="tabs tabs-boxed">
                                <a class="tab tab-active" id="tab-one-time">One-time Purchase</a>
                                <a class="tab" id="tab-subscription">Subscription</a>
                            </div>
                        @else
                            <div class="tabs tabs-boxed">
                                <a class="tab" id="tab-one-time">One-time Purchase</a>
                                <a class="tab tab-active" id="tab-subscription">Subscription</a>
                            </div>
                        @endif
                    </div>

                    <!-- One-time Credit Packages -->
                    <div id="one-time-packages" @if(!$hasActiveSubscription) class="hidden" @endif>
                        @if($hasActiveSubscription)
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                @foreach($products as $product)
                                    <div class="card bg-base-200 shadow-md">
                                        <div class="card-body">
                                            <h4 class="card-title">{{ $product->name }}</h4>
                                            <p class="text-3xl font-bold text-primary">{{ number_format($product->credits) }}</p>
                                            <p class="text-base-content/70">credits</p>
                                            <div class="mt-4">
                                                <p class="text-xl font-semibold">{{ $product->formatted_price }}</p>
                                            </div>
                                            <div class="card-actions justify-end mt-4">
                                                <a href="{{ route('credits.purchase.form', ['product' => $product->id]) }}" class="btn btn-primary">
                                                    Purchase
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="alert alert-warning">
                                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                                <div>
                                    <h3 class="font-bold">Subscription Required</h3>
                                    <div class="text-xs">One-time credit purchases are only available to customers with active subscriptions. Please subscribe to a plan first to unlock the ability to purchase additional credits.</div>
                                </div>
                            </div>
                            <div class="mt-6 text-center">
                                <a href="{{ route('subscriptions.index') }}" class="btn btn-primary">
                                    <span>View Subscription Plans</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                </a>
                            </div>
                        @endif
                    </div>

                    <!-- Subscription Options -->
                    <div id="subscription-packages" @if($hasActiveSubscription) class="hidden" @endif>
                        <div class="alert alert-info mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                            <div>
                                <span class="font-bold">Save with monthly subscriptions!</span>
                                <p class="text-sm mt-1">Get a steady flow of credits each month and save compared to one-time purchases.</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Pro Se Basic -->
                            <div class="card bg-base-200 shadow-md">
                                <div class="card-body">
                                    <h4 class="card-title">Basic Plan</h4>
                                    <p class="text-3xl font-bold text-primary">2,000</p>
                                    <p class="text-base-content/70">credits monthly</p>
                                    <div class="mt-4">
                                        <p class="text-xl font-semibold">$19.00<span class="text-base font-normal text-base-content/70">/month</span></p>
                                    </div>
                                    <ul class="mt-2 space-y-1 text-sm">
                                        <li class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                            <span>1 active case</span>
                                        </li>
                                    </ul>
                                    <div class="card-actions justify-end mt-4">
                                        <a href="{{ route('subscriptions.index') }}" class="btn btn-primary">
                                            <span>Choose Plan</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Pro Se Standard -->
                            <div class="card bg-base-200 shadow-md border-2 border-primary">
                                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-content px-4 py-1 rounded-full text-sm font-bold">
                                    Most Popular
                                </div>
                                <div class="card-body">
                                    <h4 class="card-title">Standard Plan</h4>
                                    <p class="text-3xl font-bold text-primary">6,000</p>
                                    <p class="text-base-content/70">credits monthly</p>
                                    <div class="mt-4">
                                        <p class="text-xl font-semibold">$49.00<span class="text-base font-normal text-base-content/70">/month</span></p>
                                    </div>
                                    <ul class="mt-2 space-y-1 text-sm">
                                        <li class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                            <span>2-3 cases</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                            <span>Enhanced AI capabilities</span>
                                        </li>
                                    </ul>
                                    <div class="card-actions justify-end mt-4">
                                        <a href="{{ route('subscriptions.index') }}" class="btn btn-primary">
                                            <span>Choose Plan</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <!-- Pro Se Plus -->
                            <div class="card bg-base-200 shadow-md">
                                <div class="card-body">
                                    <h4 class="card-title">Plus Plan</h4>
                                    <p class="text-3xl font-bold text-primary">25,000</p>
                                    <p class="text-base-content/70">credits monthly</p>
                                    <div class="mt-4">
                                        <p class="text-xl font-semibold">$99.00<span class="text-base font-normal text-base-content/70">/month</span></p>
                                    </div>
                                    <ul class="mt-2 space-y-1 text-sm">
                                        <li class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                            <span>Unlimited case files</span>
                                        </li>
                                        <li class="flex items-start">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                            <span>All features</span>
                                        </li>
                                    </ul>
                                    <div class="card-actions justify-end mt-4">
                                        <a href="{{ route('subscriptions.index') }}" class="btn btn-primary">
                                            <span>Choose Plan</span>
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-6 text-center">
                            <a href="{{ route('subscriptions.index') }}" class="link link-primary inline-flex items-center">
                                <span>View all subscription plans</span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @push('scripts')
    <script>
        // Toggle credit info section
        function toggleCreditInfo() {
            const content = document.getElementById('credit-info-content');
            const arrow = document.getElementById('credit-info-arrow');

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                arrow.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                arrow.classList.remove('rotate-180');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const tabOneTime = document.getElementById('tab-one-time');
            const tabSubscription = document.getElementById('tab-subscription');
            const oneTimePackages = document.getElementById('one-time-packages');
            const subscriptionPackages = document.getElementById('subscription-packages');
            const hasActiveSubscription = {{ $hasActiveSubscription ? 'true' : 'false' }};

            // Only add event listeners if user has active subscription
            if (hasActiveSubscription) {
                tabOneTime.addEventListener('click', function() {
                    // Update tabs
                    tabOneTime.classList.add('tab-active');
                    tabSubscription.classList.remove('tab-active');

                    // Show/hide content
                    oneTimePackages.classList.remove('hidden');
                    subscriptionPackages.classList.add('hidden');
                });
            }

            tabSubscription.addEventListener('click', function() {
                // Update tabs
                tabSubscription.classList.add('tab-active');
                if (hasActiveSubscription) {
                    tabOneTime.classList.remove('tab-active');
                }

                // Show/hide content
                subscriptionPackages.classList.remove('hidden');
                if (hasActiveSubscription) {
                    oneTimePackages.classList.add('hidden');
                }
            });
        });
    </script>
    @endpush
</x-app-layout>
