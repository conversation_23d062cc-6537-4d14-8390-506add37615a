<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documents Shared with You</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        .title {
            font-size: 20px;
            color: #1f2937;
            margin: 0;
        }
        .sender-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 25px;
        }
        .sender-name {
            font-weight: 600;
            color: #1f2937;
            font-size: 16px;
        }
        .sender-email {
            color: #6b7280;
            font-size: 14px;
        }
        .message-section {
            margin-bottom: 25px;
        }
        .message-content {
            background-color: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #2563eb;
            font-style: italic;
        }
        .documents-section {
            margin-bottom: 30px;
        }
        .documents-title {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 15px;
        }
        .document-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            margin-bottom: 8px;
            background-color: #fafafa;
        }
        .document-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            flex-shrink: 0;
        }
        .document-info {
            flex-grow: 1;
        }
        .document-name {
            font-weight: 500;
            color: #1f2937;
            margin-bottom: 2px;
        }
        .document-size {
            font-size: 12px;
            color: #6b7280;
        }
        .cta-section {
            text-align: center;
            margin: 30px 0;
        }
        .cta-button {
            display: inline-block;
            background-color: #2563eb;
            color: white;
            text-decoration: none;
            padding: 12px 30px;
            border-radius: 6px;
            font-weight: 600;
            font-size: 16px;
            transition: background-color 0.2s;
        }
        .cta-button:hover {
            background-color: #1d4ed8;
        }
        .expiry-info {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            text-align: center;
        }
        .expiry-text {
            color: #92400e;
            font-size: 14px;
            margin: 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        .security-note {
            background-color: #ecfdf5;
            border: 1px solid #10b981;
            padding: 12px;
            border-radius: 6px;
            margin-top: 20px;
        }
        .security-text {
            color: #047857;
            font-size: 13px;
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">Justice Quest</div>
            <h1 class="title">Documents Shared with You</h1>
        </div>

        <div class="sender-info">
            <div class="sender-name">{{ $sender->name }}</div>
            <div class="sender-email">{{ $sender->email }}</div>
        </div>

        @if($shareToken->message)
        <div class="message-section">
            <h3 style="margin-bottom: 10px; color: #1f2937;">Personal Message:</h3>
            <div class="message-content">
                {{ $shareToken->message }}
            </div>
        </div>
        @endif

        <div class="documents-section">
            <h3 class="documents-title">
                {{ count($documents) === 1 ? 'Shared Document' : 'Shared Documents' }} ({{ count($documents) }})
            </h3>
            
            @foreach($documents as $document)
            <div class="document-item">
                <div class="document-icon">
                    @if(str_starts_with($document->mime_type, 'image/'))
                        📷
                    @elseif(str_starts_with($document->mime_type, 'video/'))
                        🎥
                    @elseif(str_starts_with($document->mime_type, 'audio/'))
                        🎵
                    @elseif($document->mime_type === 'application/pdf')
                        📄
                    @else
                        📎
                    @endif
                </div>
                <div class="document-info">
                    <div class="document-name">{{ $document->title ?: $document->original_filename }}</div>
                    <div class="document-size">{{ number_format($document->file_size / 1024 / 1024, 2) }} MB</div>
                </div>
            </div>
            @endforeach
        </div>

        <div class="expiry-info">
            <p class="expiry-text">
                ⏰ This link expires on {{ $expiresAt->format('F j, Y \a\t g:i A') }}
            </p>
        </div>

        <div class="cta-section">
            <a href="{{ $shareUrl }}" class="cta-button">
                View & Download Documents
            </a>
        </div>

        <div class="security-note">
            <p class="security-text">
                🔒 This is a secure, time-limited link. Only you can access these documents using this link.
            </p>
        </div>

        <div class="footer">
            <p>
                This email was sent from Justice Quest.<br>
                If you have any questions, please reply to this email or contact {{ $sender->name }} directly.
            </p>
            <p style="margin-top: 15px; font-size: 12px;">
                Justice Quest - Legal Document Management Platform
            </p>
        </div>
    </div>
</body>
</html>
