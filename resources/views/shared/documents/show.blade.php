<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shared Documents - Justice Quest</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .document-preview {
            max-height: 70vh;
            overflow: auto;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Shared Documents</h1>
                    <p class="text-gray-600">From {{ $sender->name }} ({{ $sender->email }})</p>
                </div>
                <div class="text-right">
                    <div class="text-sm text-gray-500">Expires</div>
                    <div class="text-sm font-medium text-orange-600">
                        {{ $shareToken->expires_at->format('M j, Y \a\t g:i A') }}
                    </div>
                </div>
            </div>

            @if($shareToken->message)
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 class="font-medium text-blue-900 mb-2">Personal Message:</h3>
                <p class="text-blue-800">{{ $shareToken->message }}</p>
            </div>
            @endif
        </div>

        <!-- Documents List -->
        <div class="space-y-4">
            @foreach($documents as $document)
            <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
                <div class="p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <div class="text-2xl mr-3">
                                    @if(str_starts_with($document->mime_type, 'image/'))
                                        📷
                                    @elseif(str_starts_with($document->mime_type, 'video/'))
                                        🎥
                                    @elseif(str_starts_with($document->mime_type, 'audio/'))
                                        🎵
                                    @elseif($document->mime_type === 'application/pdf')
                                        📄
                                    @else
                                        📎
                                    @endif
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        {{ $document->title ?: $document->original_filename }}
                                    </h3>
                                    <p class="text-sm text-gray-500">
                                        {{ number_format($document->file_size / 1024 / 1024, 2) }} MB
                                        • {{ ucfirst(str_replace('/', ' ', $document->mime_type)) }}
                                    </p>
                                </div>
                            </div>
                            
                            @if($document->description)
                            <p class="text-gray-600 mb-4">{{ $document->description }}</p>
                            @endif
                        </div>
                        
                        <div class="ml-4">
                            <a href="{{ route('shared.documents.download', ['token' => $shareToken->token, 'document' => $document->id]) }}" 
                               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Download
                            </a>
                        </div>
                    </div>

                    <!-- Media Preview -->
                    @if(str_starts_with($document->mime_type, 'video/'))
                    <div class="document-preview mb-4">
                        <video controls class="w-full max-h-96 rounded-lg">
                            <source src="{{ route('shared.documents.download', ['token' => $shareToken->token, 'document' => $document->id]) }}" 
                                    type="{{ $document->mime_type }}">
                            Your browser doesn't support video playback.
                        </video>
                    </div>
                    @elseif(str_starts_with($document->mime_type, 'audio/'))
                    <div class="document-preview mb-4">
                        <audio controls class="w-full">
                            <source src="{{ route('shared.documents.download', ['token' => $shareToken->token, 'document' => $document->id]) }}" 
                                    type="{{ $document->mime_type }}">
                            Your browser doesn't support audio playback.
                        </audio>
                    </div>
                    @elseif(str_starts_with($document->mime_type, 'image/'))
                    <div class="document-preview mb-4">
                        <img src="{{ route('shared.documents.download', ['token' => $shareToken->token, 'document' => $document->id]) }}" 
                             alt="{{ $document->title ?: $document->original_filename }}"
                             class="max-w-full h-auto rounded-lg shadow-sm">
                    </div>
                    @elseif($document->mime_type === 'application/pdf')
                    <div class="document-preview mb-4">
                        <iframe src="{{ route('shared.documents.download', ['token' => $shareToken->token, 'document' => $document->id]) }}"
                                class="w-full h-96 rounded-lg border"
                                frameborder="0"></iframe>
                    </div>
                    @else
                    <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                        <div class="text-4xl mb-2">📎</div>
                        <p class="text-gray-600">Preview not available for this file type</p>
                        <p class="text-sm text-gray-500 mt-1">Click download to view the file</p>
                    </div>
                    @endif
                </div>
            </div>
            @endforeach
        </div>

        <!-- Footer -->
        <div class="mt-8 text-center">
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 inline-block">
                <div class="flex items-center text-green-800">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    <span class="text-sm font-medium">Secure, time-limited access</span>
                </div>
            </div>
            
            <div class="mt-4 text-sm text-gray-500">
                <p>Powered by <span class="font-semibold text-blue-600">Justice Quest</span></p>
                <p class="mt-1">Legal Document Management Platform</p>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh temporary URLs for media files every 30 minutes
        setTimeout(function() {
            if (document.querySelector('video, audio')) {
                location.reload();
            }
        }, 30 * 60 * 1000); // 30 minutes
    </script>
</body>
</html>
