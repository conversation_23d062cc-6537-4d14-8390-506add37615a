<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold leading-tight text-base-content">
                {{ __('app.create_new_draft') }}
            </h2>
            <a href="{{ route('case-files.drafts.index', $caseFile) }}" class="btn btn-ghost btn-sm">
                ← {{ __('app.back_to_drafts') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
            <!-- Template Selection Card -->
{{--            <div class="mb-6 overflow-hidden shadow-xl bg-base-100 sm:rounded-lg">--}}
{{--                <div class="p-6">--}}
{{--                    <h3 class="mb-4 text-lg font-medium text-base-content">{{ __('app.use_template') }}</h3>--}}
{{--                    <p class="mb-4 text-base-content/70">--}}
{{--                        {{ __('app.select_template_quick') }}</p>--}}

{{--                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">--}}
{{--                        @foreach (\App\Models\DocumentTemplate::where('is_active', true)->orderBy('name')->get() as $template)--}}
{{--                            <div class="transition-colors card bg-base-200 hover:bg-base-300">--}}
{{--                                <div class="card-body">--}}
{{--                                    <h2 class="card-title">{{ $template->name }}</h2>--}}
{{--                                    <p class="text-sm text-base-content/70">--}}
{{--                                        {{ Str::limit($template->description, 100) }}</p>--}}
{{--                                    <div class="flex items-center mt-2 mb-4">--}}
{{--                                        <span--}}
{{--                                            class="capitalize badge badge-primary">{{ $template->document_type }}</span>--}}
{{--                                    </div>--}}
{{--                                    <div class="justify-end card-actions">--}}
{{--                                        <form action="{{ route('document-templates.create-draft', $template) }}"--}}
{{--                                            method="POST">--}}
{{--                                            @csrf--}}
{{--                                            <input type="hidden" name="case_file_id" value="{{ $caseFile->id }}">--}}
{{--                                            <input type="hidden" name="description" value="{{ $template->name }}">--}}

{{--                                            @php--}}
{{--                                                $strategies = $caseFile->strategies()->latest()->get();--}}
{{--                                            @endphp--}}

{{--                                            @if($strategies->count() > 0)--}}
{{--                                                <div class="mb-3">--}}
{{--                                                    <label for="strategy_id_{{ $template->id }}" class="block mb-1 text-xs font-medium text-base-content">--}}
{{--                                                        {{ __('Apply Strategy') }}--}}
{{--                                                    </label>--}}
{{--                                                    <select id="strategy_id_{{ $template->id }}" name="strategy_id" class="select select-bordered select-sm w-full">--}}
{{--                                                        <option value="">{{ __('None') }}</option>--}}
{{--                                                        @foreach($strategies as $strategy)--}}
{{--                                                            <option value="{{ $strategy->id }}">{{ $strategy->title ?: 'Strategy ' . $strategy->version }}</option>--}}
{{--                                                        @endforeach--}}
{{--                                                    </select>--}}
{{--                                                </div>--}}
{{--                                            @endif--}}

{{--                                            <button type="submit" class="btn btn-primary btn-sm">--}}
{{--                                                {{ __('app.use_template_btn') }}--}}
{{--                                            </button>--}}
{{--                                        </form>--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                            </div>--}}
{{--                        @endforeach--}}

{{--                        @if (\App\Models\DocumentTemplate::where('is_active', true)->count() === 0)--}}
{{--                            <div class="py-8 text-center col-span-full text-base-content/50">--}}
{{--                                <p>{{ __('app.no_templates') }}</p>--}}
{{--                                <a href="{{ route('document-templates.create') }}" class="mt-4 btn btn-primary">--}}
{{--                                    {{ __('app.create_template') }}--}}
{{--                                </a>--}}
{{--                            </div>--}}
{{--                        @endif--}}
{{--                    </div>--}}
{{--                </div>--}}
{{--            </div>--}}

            <!-- Manual Creation Card -->
            <div class="overflow-hidden shadow-xl bg-base-100 sm:rounded-lg">
                <div class="p-6">
                    <h3 class="mb-4 text-lg font-medium text-base-content">{{ __('app.create_from_scratch') }}</h3>
                    <p class="mb-4 text-base-content/70">
                        {{ __('app.create_blank_document') }}</p>

                    <form action="{{ route('case-files.drafts.store', $caseFile) }}" method="POST">
                        @csrf

                        <div class="mb-6">
                            <label for="draft_type" class="block mb-2 text-sm font-medium text-base-content">
                                {{ __('app.document_type') }} <span class="text-error">*</span>
                            </label>
                            <select id="draft_type" name="draft_type"
                                class="select select-bordered w-full @error('draft_type') select-error @enderror"
                                required>
                                <option value="" disabled selected>{{ __('app.select_document_type') }}</option>

                                <option value="pleading">{{ __('app.pleading') }}</option>
                                <option value="motion">{{ __('app.motion') }}</option>
                                <option value="affidavit">{{ __('app.affidavit') }}</option>
                                <option value="proposed_order">{{ __('app.proposed_order') }}</option>
                                <option value="certificate_of_service">{{ __('app.certificate_of_service') }}</option>
                                <option value="letter">{{ __('app.letter') }}</option>
                                <option value="contract">{{ __('app.contract') }}</option>
                            </select>
                            @error('draft_type')
                                <p class="mt-1 text-sm text-error">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="mb-6">
                            <label for="strategy_id" class="block mb-2 text-sm font-medium text-base-content">
                                {{ __('Apply Case Strategy') }}
                            </label>
                            @php
                                $strategies = $caseFile->strategies()->latest()->get();
                            @endphp

                            @if($strategies->count() > 0)
                                <select id="strategy_id" name="strategy_id" class="select select-bordered w-full">
                                    <option value="">{{ __('None - Create without strategy') }}</option>
                                    @foreach($strategies as $strategy)
                                        <option value="{{ $strategy->id }}">{{ $strategy->title ?: 'Strategy ' . $strategy->version }}</option>
                                    @endforeach
                                </select>
                                <p class="mt-1 text-xs text-base-content/70">{{ __('Applying a strategy will incorporate its recommendations into the document draft.') }}</p>
                            @else
                                <div class="alert alert-info">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                        <div>
                                            <p class="font-medium">{{ __('No strategies available') }}</p>
                                            <p class="text-sm">{{ __('Create a case strategy first to apply it to your document.') }}</p>
                                        </div>
                                    </div>
                                    <div class="flex-none">
                                        <a href="{{ route('case-files.strategy.create', $caseFile) }}" class="btn btn-sm btn-primary">
                                            {{ __('Create Strategy') }}
                                        </a>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="mb-6">
                            <label for="reference_draft_id" class="block mb-2 text-sm font-medium text-base-content">
                                {{ __('Apply Reference Document') }}
                            </label>
                            @php
                                $existingDrafts = $caseFile->drafts()->latest()->get();
                            @endphp

                            @if($existingDrafts->count() > 0)
                                <select id="reference_draft_id" name="reference_draft_id" class="select select-bordered w-full">
                                    <option value="">{{ __('None - Create without reference document') }}</option>
                                    @foreach($existingDrafts as $existingDraft)
                                        <option value="{{ $existingDraft->id }}">{{ $existingDraft->description }} ({{ ucfirst($existingDraft->draft_type) }})</option>
                                    @endforeach
                                </select>
                                <p class="mt-1 text-xs text-base-content/70">{{ __('Applying a reference document will incorporate its content as context when generating this document.') }}</p>
                            @else
                                <p class="text-xs text-base-content/70">{{ __('No existing documents available to use as reference.') }}</p>
                            @endif
                        </div>

                        <div class="mb-6">
                            <label for="description" class="block mb-2 text-sm font-medium text-base-content">
                                {{ __('app.description') }} <span class="text-error">*</span>
                            </label>
                            <textarea id="description" name="description" rows="3"
                                class="textarea textarea-bordered w-full @error('description') textarea-error @enderror"
                                placeholder="{{ __('app.brief_description') }}">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-error">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="btn btn-primary">
                                {{ __('app.create_draft') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
