<div>
    <!-- Fullscreen Chat Interface -->
    <div x-data="{
        showThreadSelector: false,
        showScrollButton: false,
        scrollToBottom() {
            const chatMessages = document.getElementById('chat-messages');
            if (chatMessages) {
                // Find the last message element
                const lastMessage = chatMessages.querySelector('.chat:last-child, .mb-4:last-child');

                if (lastMessage) {
                    // Scroll to the last message with smooth behavior
                    lastMessage.scrollIntoView({ behavior: 'smooth', block: 'start' });
                } else {
                    // Fallback to scrolling to bottom if no message is found
                    chatMessages.scrollTo({ top: chatMessages.scrollHeight, behavior: 'smooth' });
                }

                // Hide the scroll button after scrolling
                this.showScrollButton = false;
            }
        },
        scrollToVeryBottom() {
            setTimeout(function() {
                const chatMessages = document.getElementById('chat-messages');
                if (chatMessages) {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                    this.showScrollButton = false;
                }
            }, 1000)
        }
    }" 
        x-on:open-chat-interface.window="$wire.open(); $nextTick(() => scrollToBottom())"
        class="flex flex-col overflow-hidden bg-base-100 dark:bg-neutral-focus h-full w-full">
        <!-- Chat Header -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 border-b border-base-content/10 dark:border-base-content/70 gap-3">
            <div class="flex items-center w-full sm:w-auto">
                <div class="flex items-center justify-center w-10 h-10 mr-3 rounded-full bg-primary/20 flex-shrink-0">
                    <span class="text-lg">⚖️</span>
                </div>
                <div class="overflow-hidden">
                    <div class="flex flex-wrap items-center gap-2">
                        <h2 class="text-lg font-semibold">{{ __('strategy.title') }}</h2>
                        @if ($caseFile)
                            <span class="px-2 py-1 text-xs rounded bg-primary/20 text-primary whitespace-nowrap">{{ __('chat.case') }}</span>
                            <span class="text-sm font-medium truncate max-w-[150px] sm:max-w-[200px]">{{ $caseFile->title }}</span>
                        @endif
                    </div>
                    <p class="text-sm text-base-content/60 truncate max-w-full">
                        {{ $currentThread ? $currentThread->title : __('chat.select_conversation_thread') }}
                    </p>
                </div>
            </div>
            <div class="flex items-center gap-2 self-end sm:self-auto w-full sm:w-auto justify-end">
                <button wire:click="saveAsStrategy" class="btn btn-sm btn-primary" title="{{ __('strategy.conversation.save_strategy') }}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                    </svg>
                    <span class="hidden sm:inline-block">{{ __('strategy.conversation.save_strategy') }}</span>
                </button>
            </div>
        </div>

        <div class="flex flex-col flex-1 overflow-hidden w-full max-w-4xl mx-auto">
            <!-- Chat Messages Area -->
            <div class="flex-1 p-4 space-y-4 overflow-y-auto h-full w-full relative" id="chat-messages"
                 x-init="
                    scrollToBottom();
                    $el.addEventListener('scroll', () => {
                        // Show button if not at bottom (with a small threshold)
                        const isAtBottom = $el.scrollHeight - $el.scrollTop - $el.clientHeight < 100;
                        if (!isAtBottom && $wire.chatMessages && $wire.chatMessages.length > 0) {
                            showScrollButton = true;
                        }
                    });
                 "
                 x-intersect:enter="scrollToBottom()"
                 @messages-updated.window="scrollToBottom()"
                 @new-assistant-message.window="showScrollButton = true; scrollToBottom()"
                 x-effect="if($wire.chatMessages && $wire.chatMessages.length) { $nextTick(() => scrollToBottom()); }">
                @if ($currentThread)
                    @foreach ($chatMessages as $message)
                        @if ($message->role === 'user')
                            <div class="chat chat-end">
                                <div class="chat-header">
                                    {{ __('chat.you') }}
                                    <time class="text-xs opacity-50">{{ $message->created_at->format('H:i') }}</time>
                                </div>
                                <div class="chat-bubble chat-bubble-secondary">
                                    {{ $message->content }}
                                </div>
                            </div>
                        @else
                            <div class="mb-4" @if($loop->last) style="background: lightgrey !important;padding: 20px;border-radius: 10px;" @endif>
                                <div class="flex items-center mb-1">
                                    <span class="font-medium">{{ __('chat.ai_assistant') }}</span>
                                    <time class="ml-2 text-xs opacity-50">{{ $message->created_at->format('H:i') }}</time>
                                </div>
                                <div class="prose prose-base max-w-none dark:prose-invert">
                                    {!! Str::markdown($message->content) !!}
                                </div>
                            </div>
                        @endif
                    @endforeach

                    <!-- Loading Animation for Assistant Response -->
                    <div x-show="$wire.isWaitingForResponse" x-init="$watch('$wire.isWaitingForResponse', value => { if (value) scrollToBottom() })" class="mb-4 animate-pulse">
                        <div class="flex items-center mb-1">
                            <span class="font-medium">{{ __('chat.ai_assistant') }}</span>
                            <span class="ml-2 text-xs opacity-50">{{ __('chat.thinking') }}</span>
                        </div>
                        <div class="p-4 rounded-lg bg-base-200">
                            <div class="flex space-x-2">
                                <div class="w-3 h-3 rounded-full bg-primary"></div>
                                <div class="w-3 h-3 rounded-full bg-primary animation-delay-200"></div>
                                <div class="w-3 h-3 rounded-full bg-primary animation-delay-400"></div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="flex items-center justify-center h-full">
                        <div class="max-w-md p-6 text-center rounded-lg bg-base-200">
                            <h3 class="mb-2 text-lg font-semibold">{{ __('strategy.conversation.start_prompt') }}</h3>
                            <p class="mb-4">{{ __('strategy.conversation.tip') }}</p>
                        </div>
                    </div>
                @endif

                <!-- Floating Scroll to Bottom Button -->
                <div x-show="showScrollButton" x-transition class="tooltip tooltip-left" data-tip="Scroll to latest message">
                    <button
                        @click="scrollToBottom()"
                        class="scroll-to-bottom-btn fixed bottom-44 right-6 bg-primary hover:bg-primary-focus text-white rounded-full p-3 shadow-lg transition-all duration-300 transform hover:scale-110 focus:outline-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Chat Input Area -->
            @include('partials.chat-interface-input')
        </div>

        <style>
            /* Fixed height for textarea */
            textarea.textarea {
                height: 80px !important;
                max-height: 80px !important;
                resize: none !important;
            }

            /* Ensure proper layout */
            .flex.flex-col {
                height: 100%;
                max-height: 100%;
                width: 100% !important;
                max-width: 100% !important;
                overflow: hidden;
            }

            #chat-messages {
                flex: 1;
                overflow-y: auto;
                padding-bottom: 20px;
                width: 100% !important;
                max-width: 100% !important;
                position: relative;
            }

            /* Floating scroll to bottom button styles */
            .scroll-to-bottom-btn {
                z-index: 90000;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
                animation: pulse-light 2s infinite;
            }

            @keyframes pulse-light {
                0% {
                    box-shadow: 0 0 0 0 rgba(var(--p), 0.4);
                }
                70% {
                    box-shadow: 0 0 0 10px rgba(var(--p), 0);
                }
                100% {
                    box-shadow: 0 0 0 0 rgba(var(--p), 0);
                }
            }

            /* Mobile responsiveness fixes */
            @media (max-width: 640px) {
                /* Ensure the chat interface has proper overflow handling */
                .flex.flex-col {
                    flex-direction: column;
                    height: 100% !important;
                    width: 100% !important;
                    max-width: 100% !important;
                }

                /* Make sure the chat messages area takes appropriate space */
                #chat-messages {
                    flex: 1;
                    overflow-y: auto;
                }

                /* Adjust scroll to bottom button position on mobile */
                .scroll-to-bottom-btn {
                    bottom: 80px;
                    right: 16px;
                }

                /* Ensure the input area doesn't overflow */
                .p-4.border-t {
                    padding-bottom: env(safe-area-inset-bottom, 1rem);
                    flex-shrink: 0;
                }
            }
        </style>
    </div>
</div>
