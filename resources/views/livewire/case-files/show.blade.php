<div class="overflow-hidden shadow-xl bg-base-100 sm:rounded-lg">
    <!-- Main Case Information -->
    <div class="p-6 border-b border-base-content/10">
        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
            <div class="space-y-1">
                <h3 class="text-2xl font-medium text-base-content">
                    {{ $caseFile->title }}
                </h3>
                <p class="text-base text-base-content/60">
                    {{ __('cases.case_number') }}: {{ $caseFile->case_number ?: __('cases.status.not_assigned') }}
                </p>
            </div>
            <div class="flex items-center">
                <div class="grid grid-cols-1 sm:flex sm:flex-wrap gap-2 w-full">
                    <button class="btn btn-sm btn-primary" wire:click="openChat">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <span>{{ __('cases.research.chat_with_assistant') }}</span>
                    </button>

                    @if ($canCreateInvoices && $caseFile->user_id === auth()->id())
                        <button class="btn btn-sm btn-accent" wire:click="createInvoice">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <span>Invoice Client</span>
                        </button>
                    @endif

                    <a href="{{ route('interview.show', [$caseFile->id, 'step' => 1, 'edit' => true]) }}"
                        class="btn btn-neutral btn-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        {{ __('cases.actions.edit') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Primary Case Workflow -->
    <div class="p-6 border-t bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
        <div class="mb-6 text-center">
            <h3 class="text-2xl font-bold text-primary mb-2">Primary Case Workflow</h3>
            <p class="text-base-content/70 max-w-2xl mx-auto">
                Follow these four essential steps to build your case from start to finish. Each step builds on the previous one to create a comprehensive legal strategy.
            </p>
        </div>

        <!-- Step 1: Case Interview (Full Width) -->
        <div class="mb-6">
            <div class="relative shadow-xl card bg-base-100 border-2 border-primary/20 max-w-2xl mx-auto">
                <!-- Workflow connector arrow pointing down -->
                <div class="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
                    <svg class="w-6 h-6 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                    </svg>
                </div>

                <div class="card-body">
                    <div class="mb-3">
                        <span class="badge badge-primary badge-lg font-semibold">Step 1</span>
                    </div>
                    <h2 class="card-title">
                        {{ __('cases.interview.title') }}
                    </h2>

                    <div class="mt-2">
                        @if ($caseFile->interview_status === \App\Models\CaseFile::INTERVIEW_NOT_STARTED)
                            <div class="alert alert-info">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                    class="w-6 h-6 stroke-current shrink-0">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{{ __('cases.interview.not_started') }}</span>
                            </div>
                        @elseif($caseFile->interview_status === \App\Models\CaseFile::INTERVIEW_IN_PROGRESS)
                            <div class="alert alert-warning">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                    class="w-6 h-6 stroke-current shrink-0">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z">
                                    </path>
                                </svg>
                                <span>{{ __('cases.interview.in_progress') }}</span>
                            </div>
                        @elseif($caseFile->interview_status === \App\Models\CaseFile::INTERVIEW_COMPLETED)
                            <div class="alert alert-success">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                    class="w-6 h-6 stroke-current shrink-0">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{{ __('cases.interview.completed') }}</span>
                            </div>
                        @endif
                    </div>

                    <div class="justify-end mt-4 card-actions">
                        <button onclick="document.getElementById('case-summary-modal').showModal()"
                            class="btn btn-secondary btn-sm">
                            {{ __('cases.interview.view_summary') }}
                        </button>
                        @if ($caseFile->interview_status !== \App\Models\CaseFile::INTERVIEW_COMPLETED)
                            <a href="{{ route('interview.show', $caseFile->id) }}" class="btn btn-primary btn-sm">
                                @if ($caseFile->interview_status === \App\Models\CaseFile::INTERVIEW_NOT_STARTED)
                                    {{ __('cases.interview.start') }}
                                @else
                                    {{ __('cases.interview.continue') }}
                                @endif
                            </a>
                        @else
                            <a href="{{ route('interview.show', $caseFile->id) }}" class="btn btn-ghost btn-sm">
                                {{ __('cases.interview.review') }}
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Steps 2-4: Research, Strategy, and Documents -->
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">

            <!-- Step 2: Legal Research -->
            <div class="transition-colors card bg-base-100 hover:bg-base-300 border-2 border-primary/20">
                <div class="card-body">
                    <div class="mb-3">
                        @if($caseFile->legalResearchItems->where('research_status', 'completed')->count() > 0)
                            <span class="badge badge-success badge-lg font-semibold">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Step 2
                            </span>
                        @else
                            <span class="badge badge-primary badge-lg font-semibold">Step 2</span>
                        @endif
                    </div>
                    <h5 class="flex items-center gap-2 card-title text-base-content">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 21H5a2 2 0 01-2-2V5a2 2 0 012-2h4m0 18h10a2 2 0 002-2v-5m-10 0l2 2 4-4" />
                        </svg>
                        {{ __('research.title') }}
                    </h5>
                    <p class="text-sm text-base-content/60">
                        {{ __('cases.quick_actions.research.description', ['default' => 'Research legal questions and get AI-powered analysis.']) }}
                    </p>
                    <div class="justify-end card-actions">
                        @if ($caseFile->interview_status !== \App\Models\CaseFile::INTERVIEW_COMPLETED)
                            <div class="alert alert-warning p-2 text-xs">
                                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-4 w-4"
                                    fill="none" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                <span>{{ __('cases.interview.complete_first') }}</span>
                            </div>
                        @else
                            <a href="{{ route('case-files.research', $caseFile) }}" class="btn btn-primary btn-sm">
                                {{ __('cases.quick_actions.research.action', ['default' => 'Research']) }}
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Step 3: Case Strategy -->
            <div class="transition-colors card bg-base-100 hover:bg-base-300 border-2 border-primary/20">
                <div class="card-body">
                    <div class="mb-3">
                        @if($caseFile->strategies->count() > 0)
                            <span class="badge badge-success badge-lg font-semibold">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Step 3
                            </span>
                        @else
                            <span class="badge badge-primary badge-lg font-semibold">Step 3</span>
                        @endif
                    </div>
                    <h5 class="flex items-center gap-2 card-title text-base-content">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                        </svg>
                        {{ __('strategy.card.title') }}
                    </h5>
                    <p class="text-sm text-base-content/60">
                        {{ __('strategy.card.description') }}</p>

                    <div class="justify-end card-actions">
                        @if ($caseFile->interview_status !== \App\Models\CaseFile::INTERVIEW_COMPLETED)
                            <div class="alert alert-warning p-2 text-xs">
                                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-4 w-4"
                                    fill="none" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                <span>{{ __('cases.interview.complete_first') }}</span>
                            </div>
                        @else
                            <div class="flex gap-2">
                                <a href="{{ route('case-files.strategies.index', $caseFile) }}"
                                    class="btn btn-primary btn-sm">
                                    {{ __('strategy.card.action') }}
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Step 4: Draft Documents -->
            <div class="transition-colors card bg-base-100 hover:bg-base-300 border-2 border-primary/20">
                <div class="card-body">
                    <div class="mb-3">
                        @if($caseFile->drafts->count() > 0)
                            <span class="badge badge-success badge-lg font-semibold">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                                Step 4
                            </span>
                        @else
                            <span class="badge badge-primary badge-lg font-semibold">Step 4</span>
                        @endif
                    </div>
                    <h5 class="flex items-center gap-2 card-title text-base-content">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        {{ __('app.draft_documents') }}
                    </h5>
                    <p class="text-sm text-base-content/60">
                        {{ __('Draft legal documents and letters') }}
                    </p>
                    <div class="justify-end card-actions">
                        <a href="{{ route('case-files.drafts.index', $caseFile) }}" class="btn btn-primary btn-sm">
                            {{ __('app.go_to_draft_dashboard') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Tools -->
    <div class="p-6 border-t bg-base-200 border-base-content/10">
        <div class="mb-6 text-center">
            <h4 class="text-xl font-bold text-base-content mb-2">Additional Tools</h4>
            <p class="text-base-content/70 max-w-2xl mx-auto">
                Supporting tools to help manage your case documents, communications, and parties.
            </p>
        </div>
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">

            <!-- Documents Section -->
            <div class="transition-colors card bg-base-100 hover:bg-base-300">
                <div class="card-body">
                    <h5 class="flex items-center gap-2 card-title text-base-content">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        {{ __('cases.quick_actions.documents.title') }}
                    </h5>
                    <p class="text-sm text-base-content/60">{{ __('cases.quick_actions.documents.description') }}</p>
                    <div class="justify-end card-actions">
                        <a href="{{ route('case-files.documents.index', $caseFile) }}"
                            class="btn btn-primary btn-sm">
                            {{ __('cases.quick_actions.documents.action') }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Correspondences Section -->
            <div class="transition-colors card bg-base-100 hover:bg-base-300">
                <div class="card-body">
                    <h5 class="flex items-center gap-2 card-title text-base-content">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-4l-4 4z" />
                        </svg>
                        {{ __('cases.quick_actions.correspondences.title') }}
                    </h5>
                    <p class="text-sm text-base-content/60">
                        {{ __('cases.quick_actions.correspondences.description') }}</p>
                    <div class="justify-end card-actions">
                        <a href="{{ route('case-files.correspondences.index', $caseFile) }}"
                            class="btn btn-primary btn-sm">
                            {{ __('cases.quick_actions.correspondences.action') }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Parties Section -->
            <div class="transition-colors card bg-base-100 hover:bg-base-300">
                <div class="card-body">
                    <h5 class="flex items-center gap-2 card-title text-base-content">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        {{ __('cases.quick_actions.parties.title') }}
                    </h5>
                    <p class="text-sm text-base-content/60">{{ __('cases.quick_actions.parties.description') }}</p>
                    <div class="justify-end card-actions">
                        <a href="{{ route('address-book.index', ['caseFileId' => $caseFile->id]) }}"
                            class="btn btn-primary btn-sm">
                            {{ __('cases.quick_actions.parties.action') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Collaborators Section -->
    <div class="p-6 border-t border-base-content/10">
        <div class="space-y-6">

            @if ($caseFile->collaboration_enabled)
                <livewire:case-collaborators.collaborators-list :case-file="$caseFile" />
            @else
                <div class="py-6 text-center">
                    <p class="text-base-content/70">
                        {{ __('cases.collaboration.not_enabled') }}
                    </p>
                    @can('update', $caseFile)
                        <button class="mt-2 btn btn-primary btn-sm" wire:click="enableCollaboration"
                            wire:loading.attr="disabled">
                            <span wire:loading.remove>
                                {{ __('cases.collaboration.enable') }}
                            </span>
                            <span wire:loading>
                                {{ __('general.processing') }}...
                            </span>
                        </button>
                    @endcan
                </div>
            @endif
        </div>
    </div>

    {{--    <!-- Legal Research Items Section --> --}}
    {{--    <div class="p-6 border-t border-base-content/10"> --}}
    {{--        <livewire:case-files.research-items :case-file="$caseFile" /> --}}
    {{--    </div> --}}

    <!-- Case Summary Modal -->
    <dialog id="case-summary-modal" class="modal modal-full">
        <div class="flex flex-col h-full max-w-full p-0 rounded-none modal-box">
            <!-- Header with title and close button -->
            <div
                class="sticky top-0 z-10 flex items-center justify-between px-6 py-4 border-b bg-base-200 border-base-300">
                <h3 class="flex items-center gap-2 text-xl font-bold">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    {{ __('cases.interview.summary_title') }}
                </h3>
                <form method="dialog">
                    <button class="btn btn-sm btn-circle btn-ghost">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </form>
            </div>

            <!-- Content area with scrolling -->
            <div class="flex-1 p-6 overflow-y-auto">
                @if ($caseFile->summaries->count() > 0)
                    <div
                        class="max-w-4xl p-8 mx-auto prose prose-lg bg-white rounded-lg shadow-sm prose-headings:text-primary prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl prose-h4:text-lg">
                        {!! Str::markdown($caseFile->summaries->first()->content) !!}
                    </div>
                @else
                    <div class="max-w-4xl p-8 mx-auto text-center rounded-lg shadow-sm bg-base-100">
                        <div class="flex flex-col items-center justify-center py-12">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-16 h-16 mb-4 text-base-300"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <h4 class="mb-2 text-xl font-semibold">{{ __('cases.interview.summary_not_available') }}
                            </h4>
                            <p class="max-w-md text-base-content/70">
                                {{ __('cases.interview.summary_after_completion') }}</p>
                        </div>
                    </div>
                @endif
            </div>

            <!-- Footer with actions -->
            <div class="sticky bottom-0 z-10 flex justify-between px-6 py-4 border-t bg-base-200 border-base-300">
                @if ($caseFile->summaries->count() > 0)
                    <button onclick="window.print()" class="gap-2 btn btn-outline">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                        </svg>
                        {{ __('cases.research.print_summary') }}
                    </button>
                @else
                    <div></div> <!-- Empty div for spacing -->
                @endif
                <form method="dialog">
                    <button class="btn btn-primary">
                        {{ __('common.close') }}
                    </button>
                </form>
            </div>
        </div>
    </dialog>
</div>
