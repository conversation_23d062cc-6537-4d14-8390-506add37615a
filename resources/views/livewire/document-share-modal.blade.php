<!-- Document Share Modal -->
<x-dialog-modal wire:model.live="showModal">
    <x-slot name="title">
        Share Documents
    </x-slot>

    <x-slot name="content">
        <form wire:submit.prevent="shareDocuments" class="space-y-6">
            <!-- Recipient Email -->
            <div>
                <label for="recipientEmail" class="block text-sm font-medium text-gray-700 mb-2">
                    Recipient Email Address
                </label>
                <input type="email"
                       id="recipientEmail"
                       wire:model="recipientEmail"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                       placeholder="Enter email address"
                       required>
                @error('recipientEmail')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Selected Documents Display -->
            @if(count($selectedDocuments) > 0)
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Selected Documents ({{ count($selectedDocuments) }})
                </label>
                <div class="space-y-2 max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3">
                    @foreach($selectedDocuments as $documentId)
                        @php
                            $document = \App\Models\Document::find($documentId);
                        @endphp
                        @if($document)
                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div class="flex items-center">
                                <span class="text-lg mr-2">
                                    @if(str_starts_with($document->mime_type, 'image/'))
                                        📷
                                    @elseif(str_starts_with($document->mime_type, 'video/'))
                                        🎥
                                    @elseif(str_starts_with($document->mime_type, 'audio/'))
                                        🎵
                                    @elseif($document->mime_type === 'application/pdf')
                                        📄
                                    @else
                                        📎
                                    @endif
                                </span>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">
                                        {{ $document->title ?: $document->original_filename }}
                                    </p>
                                    <p class="text-xs text-gray-500">
                                        {{ number_format($document->file_size / 1024 / 1024, 2) }} MB
                                    </p>
                                </div>
                            </div>
                            <button type="button"
                                    wire:click="toggleDocument({{ $document->id }})"
                                    class="text-red-600 hover:text-red-800">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        @endif
                    @endforeach
                </div>
                @error('selectedDocuments')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
            @endif

            <!-- Personal Message -->
            <div>
                <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                    Personal Message (Optional)
                </label>
                <textarea id="message"
                          wire:model="message"
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Add a personal message for the recipient..."></textarea>
                @error('message')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Expiration Settings -->
            <div>
                <label for="expiresInDays" class="block text-sm font-medium text-gray-700 mb-2">
                    Link Expires In
                </label>
                <select id="expiresInDays"
                        wire:model="expiresInDays"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="1">1 Day</option>
                    <option value="3">3 Days</option>
                    <option value="7">1 Week</option>
                    <option value="14">2 Weeks</option>
                    <option value="30">1 Month</option>
                </select>
                @error('expiresInDays')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Security Notice -->
            <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div class="flex">
                    <svg class="w-5 h-5 text-blue-400 mt-0.5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                    </svg>
                    <div>
                        <h4 class="text-sm font-medium text-blue-900">Secure Sharing</h4>
                        <p class="text-sm text-blue-700 mt-1">
                            A secure, time-limited link will be sent to the recipient.
                            The link will automatically expire after the selected time period.
                        </p>
                    </div>
                </div>
            </div>
        </form>
    </x-slot>

    <x-slot name="footer">
        <button type="button"
                wire:click="closeModal"
                class="mr-3 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
            Cancel
        </button>

        <button type="button"
                wire:click="shareDocuments"
                wire:loading.attr="disabled"
                wire:target="shareDocuments"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200">
            <span wire:loading.remove wire:target="shareDocuments" class="flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 7.89a2 2 0 002.83 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                Send Share Link
            </span>
            <span wire:loading wire:target="shareDocuments" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending Email...
            </span>
        </button>
    </x-slot>
</x-dialog-modal>
