<div class="space-y-4">
    <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium">{{ __('collaboration.headers.collaborators') }}</h3>
        <div class="flex space-x-2">
            <!-- Video Call Button -->
            <button
                type="button"
                class="btn btn-primary btn-sm"
                onclick="startCaseVideoCall('{{ $caseFile->id }}')"
                title="Start Video Call with Collaborators"
            >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Video Call
            </button>

            @can('manageCollaborators', $caseFile)
                <livewire:case-collaborators.invite :case-file="$caseFile" />
            @endcan
        </div>
    </div>

    <div class="divide-y divide-gray-200">
        @forelse($collaborators as $collaborator)
            <div class="py-4 flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <img class="h-8 w-8 rounded-full" src="{{ $collaborator->user->avatar_url }}" alt="">
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-900">{{ $collaborator->user->name }}</p>
                        <p class="text-sm text-gray-500">{{ $collaborator->user->email }}</p>
                    </div>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $collaborator->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                        {{ __('collaboration.status.' . $collaborator->status) }}
                    </span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {{ __('collaboration.roles.' . $collaborator->role) }}
                    </span>
                </div>

                @can('removeCollaborators', $caseFile)
                    <button
                        wire:click="removeCollaborator({{ $collaborator->id }})"
                        wire:confirm="{{ __('collaboration.confirmations.remove') }}"
                        class="text-red-600 hover:text-red-900"
                    >
                        {{ __('collaboration.buttons.remove') }}
                    </button>
                @endcan
            </div>
        @empty
            <p class="py-4 text-gray-500 text-center">{{ __('collaboration.messages.no_collaborators') }}</p>
        @endforelse
    </div>
</div>
