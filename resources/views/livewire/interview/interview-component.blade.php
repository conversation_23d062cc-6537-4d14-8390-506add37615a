<div
    x-data="{
        init() {
            Livewire.on('notify', (data) => {

{{--            console.log(data[0]);--}}

                if (window.showNotification) {
                    window.showNotification(data[0].message, data[0].type);
                } else {
                    alert(data[0].message);
                }
            });
        }
    }"
>
<div class="space-y-6">

    <!-- Progress indicator with clickable steps - Mobile-friendly version -->
    <div class="mb-8">


        <!-- Desktop version (hidden on small screens) -->
        <div class="hidden sm:flex justify-between items-center">

            @foreach($steps as $key => $label)
                @php
                        $stepIndex = array_search($key, array_keys($steps));
                        $isActive = $stepIndex === $currentStep;
                        $isCompleted = $completedSteps[$key];

                @endphp


                <div class="flex flex-col items-center">
                    <button
                        wire:click="goToStep({{ $stepIndex }})"
                        class="w-10 h-10 rounded-full flex items-center justify-center {{ $isActive ? 'bg-blue-600 text-white' : ($isCompleted ? 'bg-green-500 text-white' : 'bg-gray-200') }}"
                    >
                        @if($isCompleted && !$isActive)
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        @else
                            {{ $stepIndex + 1 }}
                        @endif
                    </button>
                    <span class="text-xs mt-2">{{ __("interview.steps.$key") }}</span>

                </div>


                @if($stepIndex < count($steps) - 1)
                    <div class="flex-1 h-1 bg-gray-200 mx-2">
                        <div class="h-1 bg-blue-600" style="width: {{ $stepIndex < $currentStep ? '100%' : '0%' }}"></div>
                    </div>
                @endif

            @endforeach

        </div>

        <!-- Mobile version (visible only on small screens) -->
        <div class="sm:hidden">

            <!-- Current step indicator -->
            <div class="text-center mb-4">
                <span class="text-sm text-gray-500">{{ __('interview.progress.step') }} {{ $currentStep + 1 }} {{ __('interview.progress.of') }} {{ count($steps) }}</span>
                <h3 class="text-lg font-medium">{{ __("interview.steps." . array_keys($steps)[$currentStep]) }}</h3>
            </div>

            <!-- Progress bar -->
            <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ ($currentStep / (count($steps) - 1)) * 100 }}%"></div>
            </div>

            <!-- Compact step dots -->
            <div class="flex justify-center space-x-2">

                @foreach($steps as $key => $label)
                    @php
                        $stepIndex = array_search($key, array_keys($steps));
                        $isActive = $stepIndex === $currentStep;
                        $isCompleted = $completedSteps[$key];
                    @endphp

                    <button
                        wire:click="goToStep({{ $stepIndex }})"
                        class="w-3 h-3 rounded-full {{ $isActive ? 'bg-blue-600' : ($isCompleted ? 'bg-green-500' : 'bg-gray-300') }}"
                        {{ (!$isCompleted && $stepIndex !== $currentStep) || ($key === 'ai_questions' && !$questionsGenerated) ? 'disabled' : '' }}
                        aria-label="{{ __("interview.steps.$key") }}"
                    ></button>
                @endforeach
            </div>
        </div>
    </div>



    <!-- Step content -->
    <div class="bg-white rounded-lg shadow-md p-6">
{{--        <div class="flex justify-between py-4 mb-4 border-b border-gray-200">--}}
{{--            <x-button wire:click="previousStep" class="bg-gray-200 text-gray-800 hover:bg-gray-300">--}}
{{--                {{ __('interview.navigation.back') }}--}}
{{--            </x-button>--}}
{{--            <x-button wire:click="nextStep" class="justify-center">--}}
{{--                {{ __('interview.navigation.next') }}--}}
{{--            </x-button>--}}
{{--        </div>--}}
        @if($currentStep === 0)
            <div
                x-data="{ show: false }"
                x-init="setTimeout(() => { show = true }, 100)"
                x-show="show"
                x-transition:enter="transition ease-out duration-700"
                x-transition:enter-start="opacity-0 transform -translate-y-4"
                x-transition:enter-end="opacity-100 transform translate-y-0"
                class="space-y-6"
            >
                <h3 class="text-xl font-semibold text-gray-900">{{ __('interview.intro.title') }}</h3>

                <p class="text-gray-700">
                    {{ __('interview.intro.purpose') }}
                </p>

                <p class="text-gray-700">
                    {{ __('interview.intro.process') }}
                </p>

                <ul class="list-disc pl-5 space-y-2 text-gray-700">
                    <li>{{ __('interview.intro.step1') }}</li>
                    <li>{{ __('interview.intro.step2') }}</li>
                    <li>{{ __('interview.intro.step3') }}</li>
                    <li>{{ __('interview.intro.step4') }}</li>
                    <li>{{ __('interview.intro.step5') }}</li>
                </ul>

                <p class="text-gray-700">
                    {{ __('interview.intro.time_estimate') }}
                </p>

                <div class="pt-4">
                    <x-button wire:click="nextStep" class="w-full justify-center">
                        {{ __('interview.intro.start_button') }}
                    </x-button>
                </div>
            </div>
        @elseif($currentStep === 1)
            <div
                x-data="{ show: false }"
                x-init="setTimeout(() => { show = true }, 100)"
                x-show="show"
                x-transition:enter="transition ease-out duration-700"
                x-transition:enter-start="opacity-0 transform -translate-y-4"
                x-transition:enter-end="opacity-100 transform translate-y-0"
                class="space-y-6"
            >
                <h3 class="text-xl font-semibold text-gray-900">{{ __('interview.case_identification.title') }}</h3>

                <div class="space-y-4">
                    <!-- Case Title -->
                    <div class="space-y-2">
                        <x-input-label for="title" :value="__('forms.case_title')" />
                        <x-text-input
                            id="title"
                            type="text"
                            class="mt-1 block w-full"
                            wire:model="interviewData.case_identification.title"
                            :error="$errors->has('interviewData.case_identification.title')"
                            required
                            autofocus
                            placeholder="{{ __('forms.enter_case_title') }}"
                        />
                        <x-input-error :messages="$errors->get('interviewData.case_identification.title')" />
                    </div>

                    <!-- Case Number -->
                    <div class="space-y-2">
                        <x-input-label for="case_number" :value="__('forms.case_reference_number')" />
                        <x-text-input
                            id="case_number"
                            type="text"
                            class="mt-1 block w-full"
                            wire:model="interviewData.case_identification.case_number"
                            :error="$errors->has('interviewData.case_identification.case_number')"
                            placeholder="Enter case or reference number"
                        />
                        <x-input-error :messages="$errors->get('interviewData.case_identification.case_number')" />
                    </div>

                    <!-- Case Types -->
                    <div class="space-y-2">
                        <x-input-label for="case_types" :value="__('forms.case_type')" />
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">

                            @foreach($caseTypeOptions as $type => $description)
                                <div class="form-control">
                                    <label class="cursor-pointer flex items-start gap-2">
                                        <input
                                            type="checkbox"
                                            wire:model.live="interviewData.case_identification.case_types"
                                            value="{{ $type }}"
                                            class="checkbox checkbox-primary"
                                        />
                                        <div>
                                            <span class="font-medium">{{ $type }}</span>
                                            <p class="text-sm text-base-content/70">{{ $description }}</p>
                                        </div>
                                    </label>
                                </div>
                            @endforeach
                        </div>
                        <div class="text-sm mt-1">
                            {{ $this->selectedCaseTypesCount }} {{ __('forms.categories_selected') }}
                        </div>
                        <x-input-error :messages="$errors->get('interviewData.case_identification.case_types')" />
                    </div>

                    <!-- Quick Summary -->
                    <div class="space-y-2">
                        <x-input-label for="quick_summary" :value="__('Quick Summary')" />
                        <span class="text-error text-sm">* Required</span>
                        <livewire:voice-message-input
                            name="quick_summary"
                            :value="$interviewData['case_identification']['quick_summary'] ?? []"
                            height="150px"
                            :placeholder="__('Please provide a brief summary of your case')"
                        />
                        <x-input-error :messages="$errors->get('interviewData.case_identification.quick_summary')" />
                    </div>

                    <!-- Desired Outcome -->
                    <div class="space-y-2">
                        <x-input-label for="desired_outcome" :value="__('forms.desired_outcome')" />
                        <span class="text-error text-sm">* Required</span>
                        <livewire:voice-message-input
                            name="desired_outcome"
                            :value="$interviewData['case_identification']['desired_outcome'] ?? []"
                            height="150px"
                            :placeholder="__('forms.enter_desired_outcome')"
                        />
                        <x-input-error :messages="$errors->get('interviewData.case_identification.desired_outcome')" />
                    </div>

                    <!-- Active case question -->
                    <div class="space-y-2">
                        <x-input-label for="has_active_case" :value="__('interview.case_identification.has_active_case')" />
                        <div class="flex space-x-4">
                            <label class="inline-flex items-center">
                                <x-radio wire:model.live="interviewData.case_identification.has_active_case" name="has_active_case" value="1" />
                                <span class="ml-2">{{ __('interview.case_identification.yes') }}</span>
                            </label>
                            <label class="inline-flex items-center">
                                <x-radio wire:model.live="interviewData.case_identification.has_active_case" name="has_active_case" value="0" />
                                <span class="ml-2">{{ __('interview.case_identification.no') }}</span>
                            </label>
                        </div>
                        <x-input-error :messages="$errors->get('interviewData.case_identification.has_active_case')" />
                    </div>

                    @if(isset($interviewData['case_identification']['has_active_case']) && $interviewData['case_identification']['has_active_case'])
                        <!-- User role question (only if has_active_case is true) -->
                        <div class="space-y-2">
                            <x-input-label for="user_role" :value="__('interview.case_identification.user_role')" />
                            <div class="flex space-x-4">
                                <label class="inline-flex items-center">
                                    <x-radio wire:model.live="interviewData.case_identification.user_role" name="user_role" value="plaintiff" />
                                    <span class="ml-2">{{ __('interview.case_identification.plaintiff') }}</span>
                                </label>
                                <label class="inline-flex items-center">
                                    <x-radio wire:model.live="interviewData.case_identification.user_role" name="user_role" value="defendant" />
                                    <span class="ml-2">{{ __('interview.case_identification.defendant') }}</span>
                                </label>
                            </div>
                            <x-input-error :messages="$errors->get('interviewData.case_identification.user_role')" />
                        </div>


                        <div class="space-y-2">
                            <x-input-label for="case_number" :value="__('interview.case_identification.case_number')" />
                            <x-text-input
                                id="case_number"
                                type="text"
                                class="mt-1 block w-full"
                                wire:model="interviewData.case_identification.case_number"
                                :error="$errors->has('interviewData.case_identification.case_number')"
                            />
                            <x-input-error :messages="$errors->get('interviewData.case_identification.case_number')" />
                        </div>
                    @endif

                    @if(isset($interviewData['case_identification']['has_active_case']) && !$interviewData['case_identification']['has_active_case'])
                        <!-- Alternative questions (only if has_active_case is false) -->
                        <div class="space-y-2">
                            <x-input-label :value="__('interview.case_identification.alternative_questions')" />

                            <div class="space-y-3 pl-2">
                                <!-- Considering legal action -->
                                <label class="inline-flex items-center">
                                    <x-checkbox wire:model="interviewData.case_identification.considering_legal_action" />
                                    <span class="ml-2">{{ __('interview.case_identification.considering_legal_action') }}</span>
                                </label>

                                <!-- Threatened with legal action -->
                                <label class="inline-flex items-center">
                                    <x-checkbox wire:model="interviewData.case_identification.threatened_with_legal_action" />
                                    <span class="ml-2">{{ __('interview.case_identification.threatened_with_legal_action') }}</span>
                                </label>

                                <!-- Information gathering -->
                                <label class="inline-flex items-center">
                                    <x-checkbox wire:model.live="interviewData.case_identification.information_gathering" />
                                    <span class="ml-2">{{ __('interview.case_identification.information_gathering') }}</span>
                                </label>
                            </div>
                            <x-input-error :messages="$errors->get('interviewData.case_identification.alternative_questions')" />
                        </div>
                    @endif

                    <!-- Lawyer Review Question -->
                    <div class="space-y-2 mt-6 border-t pt-6">
                        <x-input-label for="lawyer_review" :value="__('Would you like an actual lawyer to review your case?')" />
                        <span class="text-error text-sm">* Required</span>
                        <div class="flex space-x-4">
                            <label class="inline-flex items-center">
                                <x-radio wire:model.live="interviewData.case_identification.lawyer_review" name="lawyer_review" value="1" />
                                <span class="ml-2">{{ __('Yes') }}</span>
                            </label>
                            <label class="inline-flex items-center">
                                <x-radio wire:model.live="interviewData.case_identification.lawyer_review" name="lawyer_review" value="0" />
                                <span class="ml-2">{{ __('No') }}</span>
                            </label>
                        </div>
                        <x-input-error :messages="$errors->get('interviewData.case_identification.lawyer_review')" />
                    </div>
                </div>

                <div class="flex justify-between pt-4">
                    <x-button wire:click="previousStep" class="bg-gray-200 text-gray-800 hover:bg-gray-300">
                        {{ __('interview.navigation.back') }}
                    </x-button>
                    @if($edit)

                            <x-button wire:click="saveProgress" class="text-center bg-primary hover:bg-secondary">
                                {{ __('interview.navigation.save') }}
                            </x-button>

                    @endif
                    <x-button wire:click="nextStep" class="justify-center">
                        {{ __('interview.navigation.next') }}
                    </x-button>
                </div>
            </div>
        @elseif($currentStep === 2)
            <div
                x-data="{ show: false }"
                x-init="setTimeout(() => { show = true }, 100)"
                x-show="show"
                x-transition:enter="transition ease-out duration-700"
                x-transition:enter-start="opacity-0 transform -translate-y-4"
                x-transition:enter-end="opacity-100 transform translate-y-0"
                class="space-y-6"
            >
                <h3 class="text-xl font-semibold text-gray-900">{{ __('interview.exhibits.title') }}</h3>

                <div class="p-4 bg-blue-50 border-l-4 border-blue-500 mb-4">
                    <p class="text-blue-700">
                        <strong>{{ __('interview.exhibits.instructions_title') }}</strong>
                    </p>
                    <p class="text-blue-600 text-sm mt-1">
                        {{ __('interview.exhibits.upload_prompt') }}
                    </p>
                    {{--                    <p class="text-blue-600 text-sm mt-2">--}}
                    {{--                        {{ __('interview.exhibits.media_types') }}--}}
                    {{--                    </p>--}}
                </div>

                <livewire:exhibit-uploader
                    :caseFile="$caseFile"
                    folder="case_documents"
                    wire:key="exhibits-documents"
                />

                <div class="flex justify-between pt-4">
                    <x-button wire:click="previousStep" class="bg-gray-200 text-gray-800 hover:bg-gray-300">
                        {{ __('interview.navigation.back') }}
                    </x-button>
                    <x-button wire:click="nextStep" class="justify-center">
                        {{ __('interview.navigation.next') }}
                    </x-button>
                </div>
            </div>

        @elseif($currentStep === 3)
            <div
                x-data="{ show: false }"
                x-init="setTimeout(() => { show = true }, 100)"
                x-show="show"
                x-transition:enter="transition ease-out duration-700"
                x-transition:enter-start="opacity-0 transform -translate-y-4"
                x-transition:enter-end="opacity-100 transform translate-y-0"
                class="space-y-6"
            >
                <h3 class="text-xl font-semibold text-gray-900">{{ __('interview.parties.title') }}</h3>

                <p class="text-gray-700">{{ __('interview.parties.description') }}</p>

                <!-- Party Directory Component -->
                <livewire:address-book.party-directory :caseFileId="$caseFile->id" :selectable="true" />

                <div class="mt-6">
                    @if(empty($interviewData['parties'] ?? []))
                        <div class="bg-red-50 border border-red-100 text-red-700 px-4 py-3 rounded-md mb-4">
                            {{ __('interview.parties.none_selected') }}
                        </div>
                    @else
                        <h4 class="font-medium text-gray-700 mb-2">{{ __('interview.parties.selected') }}</h4>
                        <div class="space-y-2 mb-4">
                            @foreach($interviewData['parties'] as $partyId => $party)
                                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-md">
                                    <div>
                                        <span class="font-medium">{{ $party['name'] }}</span>
                                        @if(!empty($party['relationship']))
                                            <span class="text-sm text-gray-500 ml-2">({{ __('interview.relationships.' . $party['relationship']) }})</span>
                                        @endif
                                    </div>
                                    <button wire:click="removeParty({{ $partyId }})" class="text-red-500 hover:text-red-700">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>

                <div class="flex justify-between pt-4">
                    <x-button wire:click="previousStep" class="bg-gray-200 text-gray-800 hover:bg-gray-300">
                        {{ __('interview.navigation.back') }}
                    </x-button>
                    <x-button wire:click="nextStep" class="justify-center">
                        {{ __('interview.navigation.next') }}
                    </x-button>
                </div>
            </div>
        @elseif(array_keys($steps)[$currentStep] === 'overview')
            @include('livewire.interview.overview-replacement')
            <div class="flex justify-between pt-4">
                <x-button wire:click="previousStep" class="bg-gray-200 text-gray-800 hover:bg-gray-300">
                    {{ __('interview.navigation.back') }}
                </x-button>
                <x-button wire:click="nextStep" class="justify-center">
                    {{ __('interview.navigation.next') }}
                </x-button>
            </div>
        @elseif(array_keys($steps)[$currentStep] === 'ai_questions')
            <livewire:interview.a-i-questions-component :caseFile="$caseFile" />
        @elseif(array_keys($steps)[$currentStep] === 'summary')
            @include('livewire.interview.summary')
        @endif
    </div>
</div>

<!-- Document Preview Modal -->
<div x-data="{
    isPlaying: false,
    togglePlay() {
        const mediaElement = this.$refs.mediaElement;
        if (mediaElement) {
            if (this.isPlaying) {
                mediaElement.pause();
            } else {
                mediaElement.play();
            }
            this.isPlaying = !this.isPlaying;
        }
    }
}"
    x-show="$wire.showingPreviewModal"
    x-cloak
    class="fixed inset-0 z-50 overflow-y-auto"
    aria-labelledby="modal-title"
    role="dialog"
    aria-modal="true">

    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            @if($previewDocument)
                                {{ $previewDocument->title ?: $previewDocument->original_filename }}
                            @else
                                {{ __('documents.preview_document') }}
                            @endif
                        </h3>

                        <div class="mt-4 max-h-[70vh] overflow-auto">
                            @if($previewDocument && $documentUrl)
                                @if(str_starts_with($previewDocument->mime_type, 'audio/'))
                                    <div class="flex flex-col items-center justify-center p-4">
                                        <audio x-ref="mediaElement" class="w-full" controls>
                                            <source src="{{ $documentUrl }}" type="{{ $previewDocument->mime_type }}">
                                            {{ __('documents.audio_not_supported') }}
                                        </audio>
                                    </div>
                                @elseif(str_starts_with($previewDocument->mime_type, 'video/'))
                                    <div class="flex flex-col items-center justify-center p-4">
                                        <video x-ref="mediaElement" class="w-full max-h-[50vh]" controls>
                                            <source src="{{ $documentUrl }}" type="{{ $previewDocument->mime_type }}">
                                            {{ __('documents.video_not_supported') }}
                                        </video>
                                    </div>
                                @elseif(in_array($previewDocument->mime_type, ['application/pdf']))
                                    <iframe src="{{ $documentUrl }}" class="w-full h-[60vh]" frameborder="0"></iframe>
                                @elseif(str_starts_with($previewDocument->mime_type, 'image/'))
                                    <img src="{{ $documentUrl }}" alt="{{ $previewDocument->title }}" class="max-w-full max-h-[60vh] mx-auto">
                                @else
                                    <div class="p-4 text-center">
                                        <p>{{ __('documents.preview_not_available') }}</p>
                                        <a href="{{ $documentUrl }}" target="_blank" class="btn btn-primary mt-4">
                                            {{ __('documents.download_document') }}
                                        </a>
                                    </div>
                                @endif
                            @else
                                <div class="p-4 text-center">
                                    <p>{{ __('documents.loading_preview') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button"
                        wire:click="closePreviewModal"
                        class="btn btn-primary">
                    {{ __('documents.close') }}
                </button>

                @if($previewDocument && $documentUrl)
                    <a href="{{ $documentUrl }}"
                       download="{{ $previewDocument->title ?: $previewDocument->original_filename }}"
                       class="btn btn-outline mr-2">
                        {{ __('documents.download') }}
                    </a>
                @endif
            </div>
        </div>
    </div>
</div>
</div>
