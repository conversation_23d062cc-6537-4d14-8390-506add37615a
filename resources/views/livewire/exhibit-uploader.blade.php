{{--
Document Uploader Component
--------------------------

A Livewire component that provides a drag-and-drop interface for uploading documents to a case file.
This component integrates with Alpine.js for frontend interactivity and Livewire for backend communication.

Features:
- Drag and drop file upload
- Multiple file support
- File type validation (PDF, DOC, DOCX, JPG, PNG)
- File size validation (max 10MB)
- Progress indication
- Title and description fields for each document
- Individual and batch save options

Properties (Livewire):
- $files: Temporary array for newly uploaded files
- $queuedFiles: Array of files pending upload
- $documentTitles: Array of titles for queued files
- $documentDescriptions: Array of descriptions for queued files
- $caseFile: The associated CaseFile model instance

Alpine.js Component Structure:
- exhibitUploader
  - isDropping: Boolean flag for drag state
  - files: Array of file objects with metadata
  - titles: Array of document titles
  - descriptions: Array of document descriptions
  - maxFileSize: Maximum allowed file size (10MB)
  - allowedTypes: Array of allowed MIME types

Events:
- dragover: Triggers dropping state
- dragleave: Removes dropping state
- drop: Handles file drop
- change: Handles file input change

Methods:
- onFileDropped: Handles dropped files
- onFileInputChanged: Handles files selected via input
- handleFiles: Processes file list
- validateFile: Validates file type and size
- simulateUpload: Shows upload progress animation
- removeFile: Removes file from queue
- formatFileSize: Formats byte size to human-readable string

Integration Points:
- Livewire: Backend communication for file processing
- Alpine.js: Frontend interactivity and state management
- DocumentService: Backend service for document storage
- ProcessExhibitJob: Async document processing

Usage:
<livewire:exhibit-uploader :case-file="$caseFile" />
--}}

<div class="space-y-4" x-data="exhibitUploader({
    files: $wire.entangle('queuedFiles'),
    titles: $wire.entangle('documentTitles'),
    descriptions: $wire.entangle('documentDescriptions')
})" x-on:dragover.prevent="isDropping = true"
    x-on:dragleave.prevent="isDropping = false" x-on:drop.prevent="onFileDropped($event)"
    x-on:document-saved.window="handleDocumentSaved($event.detail)" class="relative">

    {{-- Upload Progress Overlay --}}
    <div x-show="isUploading || $wire.get('isSavingAll')"
        class="fixed inset-0 z-50 flex items-center justify-center bg-base-100/50 backdrop-blur-sm">
        <div class="flex flex-col items-center space-y-4">
            <div class="upload-pulse-ring"></div>
            <p class="text-base-content/70"
                x-text="$wire.get('isSavingAll') ? '{{ __('documents.saving_all_documents') }}' : uploadingMessage"></p>
        </div>
    </div>

    {{-- Credit Warning Alert --}}
    @if($insufficientCredits)
    <div class="alert alert-warning mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
        <div>
            <h3 class="font-bold">Insufficient Credits</h3>
            <div class="text-sm">You need at least {{ $requiredCredits }} credits to upload documents. Your current balance is {{ $currentBalance }} credits.</div>
            <a href="{{ route('credits.purchase.form') }}" class="btn btn-sm btn-outline mt-2">Purchase Credits</a>
        </div>
    </div>
    @endif

    {{-- File Upload Area --}}
    <label for="file-upload"
        class="relative block w-full p-12 text-center border-2 border-dashed rounded-lg border-base-content/20 hover:border-base-content/40 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
        :class="{ 'border-primary': isDropping, 'opacity-50 cursor-not-allowed': {{ $insufficientCredits ? 'true' : 'false' }} }">
        <div class="flex flex-col items-center justify-center">
            <svg class="w-12 h-12 mx-auto text-base-content/50" stroke="currentColor" fill="none"
                viewBox="0 0 48 48">
                <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <div class="mt-4 text-sm leading-6 text-base-content">
                <span class="font-semibold text-primary">{{ __('documents.click_to_upload') }}</span>
                <span class="pl-1">{{ __('documents.or_drag_and_drop') }}</span>
            </div>
            <p class="text-xs leading-5 text-base-content/60">
                {{ __('documents.allowed_file_types') }} (PDF, DOC, DOCX, JPG, PNG, MP3, WAV, M4A, OGG, MP4, MOV, WEBM)
            </p>
            <p class="text-xs leading-5 text-base-content/60">
                {{ __('documents.max_file_size') }}: 10MB (documents, images), 25MB (audio), 150MB (video)
            </p>

        </div>

        <input id="file-upload" type="file" class="hidden"
            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.mp3,.wav,.m4a,.ogg,.mp4,.mov,.webm" multiple
            x-on:change="onFileInputChanged($event)"
            {{ $insufficientCredits ? 'disabled' : '' }}>
    </label>

    {{-- File Preview Cards --}}
    {{-- File Preview Section --}}
    <div class="mt-6 space-y-4" x-show="files.length > 0">
        <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-base-content">{{ __('documents.upload_documents_title') }}</h3>
            <button type="button" wire:click="saveAllDocuments" wire:loading.attr="disabled"
                wire:target="saveAllDocuments" class="btn btn-primary" :class="{ 'loading': $wire.get('isSavingAll') }"
                {{ $insufficientCredits ? 'disabled' : '' }}>
                <span wire:loading.remove wire:target="saveAllDocuments">
                    {{ __('documents.save_all_documents') }}
                </span>
                <span wire:loading wire:target="saveAllDocuments">
                    {{ __('documents.saving_documents') }}
                </span>
            </button>
        </div>

        <template x-for="(file, index) in files" :key="index">
            <div
                class="flex flex-col p-4 space-y-3 rounded-lg md:flex-row md:items-start md:space-y-0 md:space-x-4 bg-base-200">
                <div class="flex-1">
                    <div class="flex flex-wrap items-center gap-2 p-3 mt-2 text-sm text-base-content/60">
                        <span class="p-2 text-sm font-medium rounded-full text-base-content bg-accent"
                            x-text="file.metadata.name"></span>
                        <span>{{ __('documents.file_size_label') }}: </span>
                        <span x-text="formatFileSize(file.metadata.size)"></span>

                        <!-- Exhibit Label Badge - Will be populated after save -->
                        <span x-show="file.exhibitLabel"
                            class="flex items-center gap-1 px-3 py-1 ml-auto text-sm font-semibold rounded-full bg-primary text-primary-content">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                            </svg>
                            <span x-text="file.exhibitLabel"></span>
                        </span>
                    </div>
                    <div class="mb-2">
                        <input type="text" x-model="titles[index]" class="w-full input input-bordered"
                            placeholder="{{ __('documents.document_title_placeholder') }}">
                        <div class="flex items-center gap-2 mt-1 text-sm text-info/80">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>{{ __('documents.ai_assist_notice') }}</span>
                        </div>
                    </div>
                    <div class="mt-2">
                        <textarea x-model="descriptions[index]" class="w-full textarea textarea-bordered"
                            placeholder="{{ __('documents.document_description_placeholder') }}"></textarea>
                    </div>
                </div>
                <div
                    class="flex md:flex-col items-center md:items-end justify-end gap-2 md:min-w-[100px] md:self-stretch">
                    <button type="button" x-on:click="removeFile(index)" class="text-base-content/60 hover:text-error"
                        :title="__('documents.remove_document')">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                    <template x-if="!file.isSaving">
                        <button type="button"
                            @click="file.isSaving = true; $wire.saveDocument(index); setTimeout(() => file.isSaving = false, 5000)"
                            class="btn btn-sm btn-primary">
                            {{ __('documents.save_document') }}
                        </button>
                    </template>
                    <template x-if="file.isSaving">
                        <button class="btn btn-sm btn-primary" disabled>
                            <span class="loading loading-spinner loading-sm"></span>
                            {{ __('documents.saving') }}
                        </button>
                    </template>
                </div>
            </div>
        </template>
    </div>

    {{-- Error Messages --}}
    @error('upload')
        <div class="mt-4 text-sm text-error">
            {{ $message }}
        </div>
    @enderror

    @if (session('error'))
        <div class="mt-4 text-sm text-error">
            {{ session('error') }}
        </div>
    @endif

    {{-- Document List Section --}}
    @if ($showDocumentList)
        <div class="pt-8 mt-8 border-t border-base-content/10">
            <h3 class="mb-4 text-lg font-medium text-base-content">{{ __('documents.uploaded_documents') }}</h3>
            <livewire:document-list :case-file="$caseFile" />
        </div>
    @endif

    {{-- Delete Confirmation Modal --}}
    <div x-data="{
        showDeleteModal: false,
        documentId: null,
        documentName: '',
        isDeleting: false
    }"
        @open-delete-modal.window="
            showDeleteModal = true;
            documentId = $event.detail.documentId;
            documentName = $event.detail.documentName || '{{ __('documents.this_document') }}';
        "
        x-show="showDeleteModal" x-cloak class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title"
        role="dialog" aria-modal="true">

        <div class="flex items-end justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showDeleteModal" x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
                x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100"
                x-transition:leave-end="opacity-0" class="fixed inset-0 transition-opacity bg-opacity-75 bg-base-300"
                aria-hidden="true" @click="showDeleteModal = false"></div>

            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div x-show="showDeleteModal" x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                class="inline-block px-4 pt-5 pb-4 overflow-hidden text-left align-bottom transition-all transform rounded-lg shadow-xl bg-base-100 sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">

                <div class="sm:flex sm:items-start">
                    <div
                        class="flex items-center justify-center flex-shrink-0 w-12 h-12 mx-auto rounded-full bg-error bg-opacity-20 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="w-6 h-6 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg font-medium leading-6 text-base-content" id="modal-title">
                            {{ __('documents.confirm_delete_title') }}
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-base-content/70">
                                {{ __('documents.confirm_delete_message') }} <span x-text="documentName"
                                    class="font-medium"></span>?
                                {{ __('documents.confirm_delete_warning') }}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                    <button type="button" class="btn btn-error" :class="{ 'loading': isDeleting }"
                        :disabled="isDeleting"
                        @click="isDeleting = true; $wire.deleteDocument(documentId).then(() => {
                                showDeleteModal = false;
                                isDeleting = false;
                                window.location.reload();
                            })">
                        {{ __('documents.delete') }}
                    </button>
                    <button type="button" class="mr-3 btn btn-ghost" @click="showDeleteModal = false">
                        {{ __('documents.cancel') }}
                    </button>
                </div>
            </div>
        </div>
    </div>

    {{-- Document Preview Modal --}}
    <div x-data="{
        isPlaying: false,
        togglePlay() {
            const mediaElement = this.$refs.mediaElement;
            if (mediaElement) {
                if (this.isPlaying) {
                    mediaElement.pause();
                } else {
                    mediaElement.play();
                }
                this.isPlaying = !this.isPlaying;
            }
        }
    }" x-show="$wire.showingPreviewModal" x-cloak
        class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">

        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity bg-opacity-75 bg-base-300" aria-hidden="true"></div>

            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div
                class="inline-block overflow-hidden text-left align-bottom transition-all transform rounded-lg shadow-xl bg-base-100 sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div class="px-4 pt-5 pb-4 bg-base-100 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="w-full mt-3 text-center sm:mt-0 sm:text-left">
                            <h3 class="text-lg font-medium leading-6 text-base-content" id="modal-title">
                                @if ($previewDocument)
                                    {{ $previewDocument->title }}
                                @else
                                    {{ __('documents.preview_document') }}
                                @endif
                            </h3>

                            <div class="mt-4 max-h-[70vh] overflow-auto">
                                @if ($previewDocument && $documentUrl)
                                    @if (str_starts_with($previewDocument->mime_type, 'audio/'))
                                        <div class="flex flex-col items-center justify-center p-4">
                                            <audio x-ref="mediaElement" class="w-full" controls>
                                                <source src="{{ $documentUrl }}"
                                                    type="{{ $previewDocument->mime_type }}">
                                                {{ __('documents.audio_not_supported') }}
                                            </audio>
                                            <div class="mt-4 text-sm text-base-content/70">
                                                {{ $previewDocument->description }}
                                            </div>
                                        </div>
                                    @elseif(str_starts_with($previewDocument->mime_type, 'video/'))
                                        <div class="flex flex-col items-center justify-center p-4">
                                            <video x-ref="mediaElement" class="w-full max-h-[50vh]" controls>
                                                <source src="{{ $documentUrl }}"
                                                    type="{{ $previewDocument->mime_type }}">
                                                {{ __('documents.video_not_supported') }}
                                            </video>
                                            <div class="mt-4 text-sm text-base-content/70">
                                                {{ $previewDocument->description }}
                                            </div>
                                        </div>
                                    @elseif(in_array($previewDocument->mime_type, ['application/pdf']))
                                        <iframe src="{{ $documentUrl }}" class="w-full h-[60vh]"
                                            frameborder="0"></iframe>
                                    @elseif(str_starts_with($previewDocument->mime_type, 'image/'))
                                        <img src="{{ $documentUrl }}" alt="{{ $previewDocument->title }}"
                                            class="max-w-full max-h-[60vh] mx-auto">
                                    @elseif(in_array($previewDocument->mime_type, ['application/vnd.openxmlformats-officedocument.wordprocessingml.document']))
                                        <div class="flex flex-col items-center">
                                            <div id="docx-preview-container"
                                                class="w-full h-[60vh] bg-base-100 rounded-md overflow-auto">
                                                <div class="flex items-center justify-center h-full">
                                                    <button type="button" class="btn btn-sm btn-secondary"
                                                    onclick="document.getElementById('docx-preview-container').innerHTML = 'Loading...';
                                                    setTimeout(function() {
                                                        window.dispatchEvent(new CustomEvent('docx-preview-requested', {
                                                            detail: {
                                                                documentUrl: '{{ $documentUrl }}',
                                                                containerId: 'docx-preview-container'
                                                            }
                                                        }));
                                                    }, 100);">
                                                    Click To Load Document
                                                </button>
                                                </div>
                                            </div>

                                        </div>

                                        <script>
                                            console.log('DOCX file detected in preview');
                                            // Store the document URL for testing
                                            window.currentDocxUrl = '{{ $documentUrl }}';

                                            // Dispatch event immediately and also after DOM content loaded
                                            setTimeout(function() {
                                                console.log('Dispatching docx-preview-requested event');
                                                window.dispatchEvent(new CustomEvent('docx-preview-requested', {
                                                    detail: {
                                                        documentUrl: '{{ $documentUrl }}',
                                                        containerId: 'docx-preview-container'
                                                    }
                                                }));
                                            }, 500);
                                        </script>
                                    @elseif(in_array($previewDocument->mime_type, ['text/plain', 'text/markdown', 'text/x-markdown', 'text/md']))
                                        <div class="flex flex-col items-center">
                                            <div id="text-preview-container"
                                                class="w-full h-[60vh] bg-base-100 rounded-md overflow-auto">
                                                <div class="flex items-center justify-center h-full">
                                                    <button type="button" class="btn btn-sm btn-secondary"
                                                    onclick="renderTextPreview('{{ $documentUrl }}', 'text-preview-container', '{{ $previewDocument->mime_type }}')">
                                                    Load Text Document
                                                </button>
                                                </div>
                                            </div>


                                        </div>

                                        <script>
                                            // Direct initialization for text preview
                                            console.log('Text file detected in preview: {{ $previewDocument->mime_type }}');
                                            setTimeout(function() {
                                                if (typeof renderTextPreview === 'function') {
                                                    console.log('Calling renderTextPreview directly');
                                                    renderTextPreview('{{ $documentUrl }}', 'text-preview-container',
                                                        '{{ $previewDocument->mime_type }}');
                                                } else {
                                                    console.error('renderTextPreview function not available');
                                                    document.getElementById('text-preview-container').innerHTML =
                                                        '<div class="p-4 text-center text-error"><p>Preview function not available</p></div>';
                                                }
                                            }, 1000);
                                        </script>
                                    @else
                                        <div class="p-4 text-center">
                                            <p>{{ __('documents.preview_not_available') }}</p>
                                            <a href="{{ $documentUrl }}" target="_blank"
                                                class="mt-4 btn btn-primary">
                                                {{ __('documents.download_document') }}
                                            </a>
                                        </div>
                                    @endif
                                @else
                                    <div class="p-4 text-center">
                                        <p>{{ __('documents.loading_preview') }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="px-4 py-3 bg-base-200 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" wire:click="closePreviewModal" class="btn btn-primary">
                        {{ __('documents.close') }}
                    </button>

                    @if ($previewDocument && $documentUrl)
                        <a href="{{ $documentUrl }}" download="{{ $previewDocument->title }}"
                            class="mr-2 btn btn-outline">
                            {{ __('documents.download') }}
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
