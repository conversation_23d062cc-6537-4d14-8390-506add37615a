<div class="space-y-4"
     x-data="{
         init() {
             Livewire.on('show-notification', (data) => {
                 if (window.showNotification) {
                     window.showNotification(data[0].message, data[0].type);
                 } else {
                     alert(data[0].message);
                 }
             });
         }
     }">
    <!-- Status Messages -->
{{--    @if(session('success'))--}}
{{--        <div class="alert alert-success">--}}
{{--            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>--}}
{{--            <span>{{ session('success') }}</span>--}}
{{--        </div>--}}
{{--    @endif--}}

{{--    @if(session('error'))--}}
{{--        <div class="alert alert-error">--}}
{{--            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>--}}
{{--            <span>{{ session('error') }}</span>--}}
{{--        </div>--}}
{{--    @endif--}}

    <!-- Search Input and Actions -->
    <div class="mb-4 flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div class="relative flex-1">
            <input
                type="text"
                wire:model.live="search"
                placeholder="{{ __('documents.search_documents') }}"
                class="w-full pl-10 input input-bordered"
            >
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <svg class="w-5 h-5 text-base-content/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </div>
            @if($search)
                <button
                    wire:click="$set('search', '')"
                    class="absolute inset-y-0 right-0 flex items-center pr-3"
                >
                    <svg class="w-5 h-5 text-base-content/50 hover:text-base-content" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            @endif
        </div>

        @if($documents->count() > 1)
        <button type="button"
                wire:click="$dispatch('openShareModal', { documentIds: {{ $documents->pluck('id')->toJson() }} })"
                class="btn btn-primary btn-sm">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
            </svg>
            Share All Documents
        </button>
        @endif
    </div>
{{--@dd($documents)--}}
    <!-- Documents List -->
    @forelse($documents as $document)
        <div class="card bg-base-200">
            <div class="p-4 card-body">
                <div class="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
                    <div class="flex-1">
                        <div class="flex flex-wrap items-center gap-2 mb-1">
                            <h4 class="font-medium break-words">{{ $document->title ?: $document->original_filename }}</h4>

                            @if($document->exhibit)
                                <span class="px-2 py-1 text-xs font-semibold bg-primary text-primary-content rounded-full">
                                    {{ $document->exhibit->label }}
                                </span>
                            @endif
                        </div>

                        @if($document->description)
                            <p class="text-sm text-base-content/60 break-words">{{ $document->description }}</p>
                        @endif
                        <div class="mt-1 text-xs text-base-content/60">
                            {{ number_format($document->file_size / 1024 / 1024, 2) }} MB
                            · {{ $document->created_at->diffForHumans() }}
                            · {{ __('documents.status') }}: {{ __('documents.status_' . $document->ingestion_status) }}
                        </div>
                    </div>
                    <div class="flex gap-2 mt-2 md:mt-0">
                        <button type="button"
                                wire:click="$dispatch('previewDocument', { documentId: {{ $document->id }} })"
                                class="btn btn-sm btn-outline">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            <span class="ml-1 hidden sm:inline">{{ __('documents.preview') }}</span>
                        </button>

                        <button type="button"
                                wire:click="$dispatch('openShareModal', { documentIds: [{{ $document->id }}] })"
                                class="btn btn-sm btn-outline btn-primary">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                            </svg>
                            <span class="ml-1 hidden sm:inline">Share</span>
                        </button>

                        @if($document->ingestion_status === 'failed')
                            <button type="button"
                                    wire:click="retryProcessDocument({{ $document->id }})"
                                    wire:loading.attr="disabled"
                                    wire:target="retryProcessDocument({{ $document->id }})"
                                    class="btn btn-sm btn-warning">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                <span class="ml-1 hidden sm:inline">{{ __('documents.retry') }}</span>
                            </button>
                        @endif

                        <button class="btn btn-sm btn-ghost text-error"
                                @click="$dispatch('open-delete-modal', { documentId: {{ $document->id }}, documentName: '{{ addslashes($document->title ?: $document->original_filename) }}' })"
                                title="{{ __('documents.delete_document') }}">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="py-8 text-center text-base-content/60">
            @if($search)
                {{ __('documents.no_documents_found', ['search' => $search]) }}
            @else
                {{ __('documents.no_documents_uploaded') }}
            @endif
        </div>
    @endforelse

    <!-- Pagination -->
    @if($documents->hasPages())
        <div class="mt-4">
            {{ $documents->links() }}
        </div>
    @endif

    <!-- Document Preview Modal -->
    <x-dialog-modal wire:model.live="showingPreviewModal">
        <x-slot name="title">
            {{ __('documents.document_preview') }}
        </x-slot>

        <x-slot name="content">
            @if($previewDocument)
                <div class="space-y-4">
                    <div class="aspect-auto max-h-[70vh] overflow-auto">
                        @if(str_contains($previewDocument->mime_type, 'image'))
                            <img src="{{ $this->getDocumentUrl($previewDocument->id) }}"
                                 alt="{{ $previewDocument->title }}"
                                 class="h-auto max-w-full">
                        @elseif($previewDocument->mime_type === 'application/pdf')
                            <iframe src="{{ $this->getDocumentUrl($previewDocument->id) }}"
                                    class="w-full h-[70vh]"
                                    frameborder="0"></iframe>
                        @else
                            <div class="p-4 text-center text-base-content/60">
                                Preview not available for this file type
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </x-slot>

        <x-slot name="footer">
            <button class="btn btn-ghost" wire:click="closePreviewModal">
                {{ __('documents.close') }}
            </button>
        </x-slot>
    </x-dialog-modal>

    <!-- Document Share Modal is handled by the parent document-bucket component -->
</div>
