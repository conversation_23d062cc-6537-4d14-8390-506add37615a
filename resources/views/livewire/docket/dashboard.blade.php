<div class="space-y-4">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0">
        <h2 class="text-xl sm:text-2xl font-semibold">{{ __('docket.dashboard.title') }}</h2>
        <button
            wire:click="showCreateModal"
            class="btn btn-primary btn-sm sm:btn-md w-full sm:w-auto"
        >
            <x-icon name="plus" class="w-4 h-4 sm:w-5 sm:h-5" />
            {{ __('docket.entry.create') }}
        </button>
    </div>

    <div class="bg-base-200 p-3 sm:p-4 rounded-lg space-y-3 sm:space-y-4">
        <div class="grid grid-cols-1 sm:flex sm:flex-wrap gap-3 sm:gap-4">
            <div class="w-full sm:flex-1 relative">
                <input
                    type="text"
                    wire:model.live.debounce.300ms="search"
                    placeholder="{{ __('docket.search.placeholder') }}"
                    class="input input-bordered input-sm sm:input-md w-full pl-8 sm:pl-10"
                >
                <div class="absolute inset-y-0 left-0 pl-2 sm:pl-3 flex items-center pointer-events-none">
                    <x-icon name="search" class="w-4 h-4 sm:w-5 sm:h-5 text-base-content/50" />
                </div>
                @if($search)
                    <button
                        wire:click="$set('search', '')"
                        class="absolute inset-y-0 right-0 pr-2 sm:pr-3 flex items-center"
                    >
                        <x-icon name="x-circle" class="w-4 h-4 sm:w-5 sm:h-5 text-base-content/50 hover:text-base-content" />
                    </button>
                @endif
            </div>
            <div class="grid grid-cols-2 sm:grid-cols-none sm:flex gap-3 sm:gap-4">
                <select
                    wire:model.live="entryType"
                    class="select select-bordered select-sm sm:select-md"
                >
                    <option value="">{{ __('docket.filter.all_types') }}</option>
                    @foreach($entryTypes as $type)
                        <option value="{{ $type }}">{{ __("docket.entry.types.$type") }}</option>
                    @endforeach
                </select>
                <select
                    wire:model.live="status"
                    class="select select-bordered select-sm sm:select-md"
                >
                    <option value="">{{ __('docket.filter.all_statuses') }}</option>
                    @foreach($statuses as $status)
                        <option value="{{ $status }}">{{ __("docket.entry.status.$status") }}</option>
                    @endforeach
                </select>
            </div>
        </div>
    </div>

    <div class="space-y-4">
        <div wire:loading.delay class="text-center py-4">
            <div class="loading loading-spinner loading-lg"></div>
        </div>

        <div wire:loading.delay.remove>
            @forelse($docketEntries as $entry)
                <livewire:docket.entry-card
                    :key="$entry->id"
                    :entry="$entry"
                />
            @empty
                <div class="text-center py-8 text-base-content/70">
                    {{ __('docket.empty_state') }}
                </div>
            @endforelse

            {{ $docketEntries->links() }}
        </div>
    </div>

    <livewire:docket.create-entry :case-file="$caseFile" lazy />
</div>
