# Caption Data Refactoring Plan

## Overview

This document outlines the plan to refactor how caption data is stored and managed in the Justice Quest application. Currently, caption data is embedded within the `sections_structure` JSON field of the `drafts` table. This plan proposes moving caption data to a dedicated `caption_data` column for improved maintainability, querying capabilities, and separation of concerns.

## Current Implementation

- Caption data is stored as JSON within the `sections_structure` array in the `drafts` table
- The `CaptionFormEditor` component manages caption editing and updates the corresponding section
- The `AiDocumentEditor` component coordinates updates between the editor and database
- The `DocumentGenerationService` parses caption data from the section content

## Proposed Changes

### 1. Database Changes

Add a dedicated `caption_data` JSON column to the `drafts` table:

```php
Schema::table('drafts', function (Blueprint $table) {
    $table->json('caption_data')->nullable()->after('sections_structure');
});
```

### 2. Model Updates

Update the `Draft` model to properly cast the new column:

```php
// In Draft.php
protected $casts = [
    'sections_structure' => 'array',
    'caption_data' => 'array',
    // other existing casts...
];

protected $fillable = [
    // existing fillable properties...
    'caption_data',
];
```

### 3. Component Updates

#### 3.1 CaptionFormEditor Component

Update to store data in the new column:

```php
public function updateContent($dispatchEvent = true)
{
    // Set saving state to true
    $this->isSaving = true;

    // Create a structured data object with a clear format identifier
    $data = [
        '_format' => 'caption_form_v1',
        'courtName' => $this->courtName,
        'division' => $this->division,
        'caseNumber' => $this->caseNumber,
        'judgeName' => $this->judgeName,
        'documentTitle' => $this->documentTitle,
        'plaintiffs' => $this->plaintiffs,
        'defendants' => $this->defendants
    ];

    // Convert to JSON and update the content property
    $this->content = json_encode($data);

    // Persist to database if we have a draft ID
    if ($this->draftId) {
        try {
            $draft = \App\Models\Draft::find($this->draftId);
            if ($draft) {
                // Update the draft with the caption data
                $draft->update([
                    'caption_data' => $data
                ]);
                
                // Also update the section content for backward compatibility during transition
                $sections = $draft->sections_structure;
                if (is_array($sections)) {
                    foreach ($sections as &$section) {
                        if ($section['id'] === 'caption') {
                            $section['content'] = $this->content;
                            break;
                        }
                    }
                    $draft->update(['sections_structure' => $sections]);
                }
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error saving caption content to database', [
                'error' => $e->getMessage(),
                'draftId' => $this->draftId
            ]);
        }
    }

    // Emit event to parent component if requested
    if ($dispatchEvent) {
        $this->dispatch('caption-content-updated', [
            'content' => $this->content
        ]);
    }

    // Set saving state to false
    $this->isSaving = false;
}

public function mount($content = null, $draftId = null, $caseFileId = null)
{
    // Set properties
    $this->draftId = $draftId;
    $this->caseFileId = $caseFileId;

    // If we have a draft ID, try to load caption data from the new column
    if ($this->draftId) {
        $draft = \App\Models\Draft::find($this->draftId);
        if ($draft && !empty($draft->caption_data)) {
            $this->content = json_encode($draft->caption_data);
            $this->parseContentToForm();
            return;
        }
    }

    // If we have content, parse it to populate the form fields
    if (!empty($content)) {
        $this->content = $content;
        $this->parseContentToForm();
    } else {
        // If we have a case file, pre-fill some fields
        $this->prefillFromCaseFile();
    }
}
```

#### 3.2 AiDocumentEditor Component

Update to handle caption data separately:

```php
public function captionContentUpdated($data)
{
    // Update the active section content with the caption form data
    if ($this->activeSectionId === 'caption' && isset($data['content'])) {
        \Illuminate\Support\Facades\Log::info('Caption content updated event received', [
            'contentLength' => strlen($data['content'])
        ]);

        $this->activeSectionContent = $data['content'];
        
        // Parse the content to get the caption data
        $captionData = json_decode($data['content'], true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($captionData)) {
            // Update the draft with the caption data
            $this->draft->update([
                'caption_data' => $captionData
            ]);
        }
        
        // Also update the section content for backward compatibility
        $this->updateSectionContent();
    }
}

protected function handleAiResponseForCaption($response, $sectionId)
{
    $documentContent = $response['document_content'] ?? '';
    
    // Try to extract JSON from the content
    if (
        preg_match('/```json\s*({[\s\S]*?})\s*```/s', $documentContent, $matches) ||
        preg_match('/{\s*"courtName"[\s\S]*?}/s', $documentContent, $matches)
    ) {
        $jsonContent = $matches[1] ?? $matches[0];
        
        try {
            // Clean up the JSON string
            $jsonContent = str_replace(['\\', '\\n'], ["", ""], $jsonContent);
            $jsonData = json_decode($jsonContent, true);
            
            if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
                // Add format identifier
                $jsonData['_format'] = 'caption_form_v1';
                
                // Ensure required fields exist
                $jsonData['courtName'] = $jsonData['courtName'] ?? '';
                $jsonData['division'] = $jsonData['division'] ?? '';
                $jsonData['caseNumber'] = $jsonData['caseNumber'] ?? '';
                $jsonData['judgeName'] = $jsonData['judgeName'] ?? '';
                $jsonData['documentTitle'] = $jsonData['documentTitle'] ?? '';
                $jsonData['plaintiffs'] = $jsonData['plaintiffs'] ?? [['name' => '', 'role' => 'Plaintiff']];
                $jsonData['defendants'] = $jsonData['defendants'] ?? [['name' => '', 'role' => 'Defendant']];
                
                // Re-encode with format identifier
                $formattedJson = json_encode($jsonData);
                
                // Update the draft with the caption data
                $this->draft->update([
                    'caption_data' => $jsonData
                ]);
                
                // Also update the section content for backward compatibility
                $this->updateSectionWithContent($sectionId, $formattedJson);
                
                return true;
            }
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error processing AI-generated caption', [
                'error' => $e->getMessage(),
                'jsonContent' => $jsonContent
            ]);
        }
    }
    
    return false;
}
```

### 4. Document Generation Service Updates

Update to use the dedicated caption data:

```php
protected function processCaptionSection($section, $content, Draft $draft)
{
    // Use caption_data directly instead of parsing from content
    $captionData = $draft->caption_data ?? [];
    
    // Fallback to parsing content if caption_data is empty
    if (empty($captionData) && !empty($content)) {
        try {
            $captionData = json_decode($content, true);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error parsing caption JSON', [
                'error' => $e->getMessage()
            ]);
        }
    }
    
    if (!$captionData || !is_array($captionData)) {
        // Fallback to empty caption
        $captionData = [
            'courtName' => '',
            'division' => '',
            'caseNumber' => '',
            'judgeName' => '',
            'documentTitle' => '',
            'plaintiffs' => [],
            'defendants' => []
        ];
    }
    
    // Add court name (centered, all caps)
    if (!empty($captionData['courtName'])) {
        $section->addText(
            strtoupper($captionData['courtName']),
            ['bold' => true, 'size' => 14],
            ['alignment' => 'center']
        );
    }
    
    // Rest of the method remains the same...
}
```

### 5. Data Migration

Create a migration to populate the new column from existing data:

```php
public function up()
{
    // Get all drafts
    $drafts = \App\Models\Draft::all();
    
    foreach ($drafts as $draft) {
        $captionData = null;
        
        // Find caption section in sections_structure
        if (!empty($draft->sections_structure)) {
            foreach ($draft->sections_structure as $section) {
                if ($section['id'] === 'caption' && !empty($section['content'])) {
                    try {
                        $captionData = json_decode($section['content'], true);
                        
                        // Add format identifier if missing
                        if (is_array($captionData) && !isset($captionData['_format'])) {
                            $captionData['_format'] = 'caption_form_v1';
                        }
                    } catch (\Exception $e) {
                        \Illuminate\Support\Facades\Log::error('Error parsing caption content during migration', [
                            'draft_id' => $draft->id,
                            'error' => $e->getMessage()
                        ]);
                    }
                    break;
                }
            }
        }
        
        // Update draft with caption data
        if ($captionData) {
            $draft->update(['caption_data' => $captionData]);
        }
    }
}
```

## Implementation Phases

### Phase 1: Database and Model Updates
1. Create migration to add `caption_data` column
2. Update Draft model with proper casts and fillable properties
3. Run migration to add column to database

### Phase 2: Component Updates
1. Update CaptionFormEditor to use the new column
2. Update AiDocumentEditor to handle caption data separately
3. Update DocumentGenerationService to use the dedicated caption data

### Phase 3: Data Migration and Testing
1. Run data migration to populate the new column from existing data
2. Test all components with the new structure
3. Verify document generation works correctly with the new approach

### Phase 4: Cleanup (Optional Future Phase)
1. Remove caption section from sections_structure
2. Update components to only use caption_data
3. Remove backward compatibility code

## Benefits of This Approach

1. **Improved Data Structure**: Caption data is stored in a dedicated column, making it easier to query and validate
2. **Better Separation of Concerns**: Structured caption data is separated from unstructured content
3. **Enhanced Maintainability**: Code is more focused and purpose-specific
4. **Backward Compatibility**: The transition maintains compatibility with existing code
5. **Future Extensibility**: Makes it easier to add more caption-specific features in the future

## Risks and Mitigations

| Risk | Mitigation |
|------|------------|
| Data loss during migration | Maintain data in both locations during transition |
| Compatibility issues | Implement backward compatibility in all components |
| Performance impact | Monitor query performance and optimize as needed |
| Code complexity | Document the transition and remove dual-storage once stable |

## Conclusion

This refactoring plan provides a clear path to improve the data structure and maintainability of caption handling in the Justice Quest application. By moving caption data to a dedicated column, we achieve better separation of concerns while maintaining backward compatibility during the transition.