# Voice Message Input

## Overview
The Voice Message Input is a reusable Livewire component that enables users to record or upload voice messages, which are automatically transcribed using OpenAI's Whisper AI. The transcribed text is then inserted into a textarea for editing.

## Technical Details

### Core Components

1. **Livewire Component**
   - Class: `App\Livewire\VoiceMessageInput`
   - View: `resources/views/livewire/voice-message-input.blade.php`
   - Uses: `WithFileUploads` trait for handling audio uploads

2. **Frontend Technologies**
   - Alpine.js for client-side interactivity
   - MediaRecorder API for voice recording
   - Fetch API for file uploads
   - CSRF protection integration

3. **Backend Processing**
   - OpenAI Whisper API for transcription
   - Audio format conversion when needed
   - File size and type validation
   - Asynchronous processing for large files

### Component Properties

```php
class VoiceMessageInput
{
    public $message;      // Current textarea content
    public $height;       // Textarea height
    public $name;         // Input field name
    public $isRecording;  // Recording state
}
```

### Implementation Details

1. **Recording Functionality**
   ```javascript
   startRecording()
   ├── getUserMedia()    // Access microphone
   ├── MediaRecorder     // Record audio
   └── ondataavailable   // Collect audio chunks
   ```

2. **File Upload Support**
   - Supported formats: WAV, MP3, MPEG, M4A, MP4, WebM, OGG
   - Maximum file size: 25MB
   - Automatic format detection and handling

3. **Transcription Flow**
   ```
   uploadAudioBlob()
   ├── Validate size/type
   ├── Create FormData
   ├── POST to /transcribe
   └── Append transcription
   ```

4. **Enhanced Error Handling**
   ```javascript
   try {
     // Upload and transcribe
   } catch (error) {
     // Detailed error logging
     console.error('Error details:', {
       message: error.message,
       stack: error.stack,
       name: error.name
     });
   }
   ```

5. **Timeout Management**
   - Extended timeout configuration for OpenAI API calls
   - Custom HTTP client with increased timeout settings
   - Detailed logging for troubleshooting

### Usage Examples

1. **Basic Implementation**
```php
<livewire:voice-message-input
    name="message_field"
    height="150px"
    wire:model="messageContent"
/>
```

2. **With Event Handling**
```php
<livewire:voice-message-input
    name="case_overview"
    :value="$initialText"
    wire:model.debounce.500ms="caseData.overview.text"
    height="200px"
/>
```

## Dependencies

- Laravel Livewire
- Alpine.js
- OpenAI Whisper API
- Browser MediaRecorder API
- CSRF Token Management

## Integration Points

1. **Address Book**
   - Party information dictation
   - Bulk contact processing

2. **Case Management**
   - Case summaries
   - Initial case overview
   - Desired outcome documentation

3. **Interview System**
   - Question responses
   - Case details recording

## Error Handling

1. **Client-side**
   - Microphone access errors
   - File size validation
   - Format validation
   - Upload errors
   - Detailed console logging for debugging

2. **Server-side**
   - Transcription failures
   - File conversion errors
   - API errors
   - Comprehensive error logging with stack traces
   - Request validation details

## Performance Considerations

1. **Timeout Management**
   - Extended timeout for large audio files
   - Custom HTTP client configuration
   - Detailed logging for performance monitoring

2. **Error Recovery**
   - Specific handling for timeout errors
   - Improved error messages for troubleshooting

## Localization

Supports multiple languages through Laravel's translation system:
- English (`resources/lang/en/voice.php`)
- Spanish (`resources/lang/es/voice.php`)

Key translations include:
- Recording instructions
- Button labels
- Error messages
- Status messages

## Security Considerations

1. **File Upload Security**
   - Size limitations
   - Type validation
   - CSRF protection
   - Secure temporary storage

2. **Audio Processing**
   - Secure transmission
   - Temporary file cleanup
   - Access control

## Best Practices

1. **Implementation**
   - Always specify a unique `name` attribute
   - Set appropriate height for context
   - Include placeholder text
   - Handle the `voice-message-updated` event

2. **User Experience**
   - Clear recording status indication
   - Visual feedback during transcription
   - Error message clarity
   - Responsive design

3. **Debugging**
   - Check browser console for detailed error logs
   - Review server logs for API interaction details
   - Monitor network requests for timeout issues

## Known Limitations

1. **Browser Support**
   - Requires modern browser with MediaRecorder support
   - Audio format support varies by browser
   - Mobile device compatibility considerations

2. **Performance**
   - Large file processing time
   - Network dependency
   - API rate limits
   - Potential timeout issues with files >10MB

## Troubleshooting

1. **Common Issues**
   - "Unknown error occurred": Check server logs for detailed error messages
   - Timeout errors: Consider reducing audio file size or quality
   - CSRF token mismatch: Ensure proper token handling

2. **Debugging Steps**
   - Check browser console for detailed error logs
   - Review Laravel logs (`storage/logs/laravel.log`)
   - Verify OpenAI API key configuration
   - Test with smaller audio files first

## Future Improvements

1. **Planned Enhancements**
   - Asynchronous processing via queue jobs
   - Real-time transcription
   - Multiple language support
   - Enhanced error recovery
   - Improved format support

2. **Potential Features**
   - Audio preview
   - Editing history
   - Format conversion options
   - Accessibility improvements
   - Progress indicators for large file uploads
