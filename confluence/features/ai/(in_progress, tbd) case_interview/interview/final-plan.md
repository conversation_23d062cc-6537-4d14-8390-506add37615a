After reviewing the proposed development plan and database schema, here's a refined final plan for implementing the AI-generated question feature:

<augment_code_snippet path="confluence/interview/final-plan.md" mode="EDIT">
## Final Development Plan: Implementing AI-Generated Question Presentation and Answering

This plan outlines the implementation steps for the AI-generated question feature, where questions are presented to users and their answers are recorded.

### Phase 1: Database Schema Updates (1-2 days)

* Add `interview_questions` table:
  ```sql
  CREATE TABLE interview_questions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    case_file_id BIGINT NOT NULL,
    question_text TEXT NOT NULL,
    expected_response_type ENUM('text', 'voice', 'document', 'date', 'multiple_choice') NOT NULL,
    multiple_choice_options JSON NULL,
    question_order INT NOT NULL DEFAULT 0,
    is_answered BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (case_file_id) REFERENCES case_files(id) ON DELETE CASCADE
  );
  ```

* Add `interview_answers` table:
  ```sql
  CREATE TABLE interview_answers (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    case_file_id BIGINT NOT NULL,
    interview_question_id BIGINT NOT NULL,
    answer_text TEXT NULL,
    document_id BIGINT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (case_file_id) REFERENCES case_files(id) ON DELETE CASCADE,
    FOREIGN KEY (interview_question_id) REFERENCES interview_questions(id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL
  );
  ```

* Add `interview_status` column to `case_files` table if not already present

### Phase 2: Backend Implementation (3-4 days)

1. **AI Question Generation Endpoint**
    * Create `POST /api/interview/generate-questions/{case_file_id}` endpoint
    * Implement logic to:
        - Gather all case information (overview, documents, parties)
        - Format data for OpenAI API
        - Send request to OpenAI assistant
        - Parse response and store questions in `interview_questions` table

2. **Question Retrieval Endpoint**
    * Create `GET /api/interview/questions/{case_file_id}` endpoint
    * Return all questions with their current answer status

3. **Answer Submission Endpoint**
    * Create `POST /api/interview/answer` endpoint
    * Accept and validate answer data
    * Store in `interview_answers` table
    * Update `is_answered` flag in `interview_questions`

4. **Document Upload Integration**
    * Modify existing document upload endpoint to support interview answers
    * Add logic to link uploaded documents to interview answers

5. **Interview Completion Endpoint**
    * Create `POST /api/interview/complete/{case_file_id}` endpoint
    * Update case interview status
    * Trigger next phase of interview workflow

### Phase 3: Frontend Implementation (5-7 days)

1. **Livewire Component Creation**
    * Create `InterviewQuestionsComponent.php` extending existing interview framework
    * Implement state management for questions and answers

2. **Question Display UI**
    * Build UI for displaying questions in a clear, organized manner
    * Implement navigation between questions
    * Add progress indicator

3. **Dynamic Input Fields**
    * Create conditional rendering based on `expected_response_type`
    * Implement text inputs, voice recorder, document uploader, date picker, and multiple choice
    * Reuse existing components where possible (voice recorder, document uploader)

4. **Answer Submission Logic**
    * Implement "Save Answer" functionality
    * Add validation for required fields
    * Provide visual feedback on successful submission

5. **Completion UI**
    * Add "Finish Questions" button
    * Implement confirmation dialog
    * Handle transition to next interview phase

### Phase 4: Testing and Refinement (2-3 days)

1. **Unit Testing**
    * Test API endpoints
    * Validate data storage and retrieval

2. **Integration Testing**
    * Test end-to-end flow
    * Verify OpenAI integration
    * Test document upload functionality

3. **UI/UX Refinement**
    * Improve error handling
    * Add loading states
    * Enhance visual feedback

### Implementation Notes

* **OpenAI Prompt Engineering**: Structure prompts to generate questions with appropriate `expected_response_type` values
* **Component Reuse**: Leverage existing `add-communication-form` for document uploads
* **Progressive Enhancement**: Implement basic functionality first, then add features like voice input
* **Error Handling**: Implement robust error handling for API failures and validation issues
* **Performance**: Consider pagination for cases with many questions
* **Security**: Validate all user inputs and ensure proper authorization checks

This implementation plan provides a structured approach to building the AI-generated question feature while leveraging existing components and following best practices.
</augment_code_snippet>
