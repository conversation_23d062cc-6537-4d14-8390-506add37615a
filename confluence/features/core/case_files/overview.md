# Case Files Architecture & Workflow

## Overview
Case Files serve as the central organizational unit in the Justice Quest system, managing all case-related information, documents, and AI assistance. Each case file represents a legal matter and coordinates various subsystems including document management, AI assistance, collaboration, and correspondence.

## Core Components

### 1. Models

#### CaseFile Model
```php
class CaseFile extends Model
{
    protected $fillable = [
        'title',
        'case_number',
        'desired_outcome',
        'user_id',
        'status',
        'filed_date',
        'openai_assistant_id',
        'openai_vector_store_id',
        'openai_project_id',
        'collaboration_enabled',
        'max_collaborators',
        'case_types',
        'date_of_incident',
        'initial_summary',
        'interview_status',
        'structured_interview_data',
    ];

    // Relationships
    - user(): BelongsTo
    - documents(): HasMany
    - summaries(): HasMany
    - openAiProject(): BelongsTo
    - threads(): HasMany
    - collaborators(): Has<PERSON>any
}
```

### 2. Observer Pattern

The `CaseFileObserver` handles lifecycle events:

```php
class CaseFileObserver
{
    // Lifecycle Events
    - created(): Sets up AI resources
    - deleted(): Cleans up AI resources
    - updated(): <PERSON>les state changes
    - restored(): Manages soft-delete restoration
    - forceDeleted(): <PERSON><PERSON> permanent deletion
}
```

### 3. Views Structure

```
case-files/
├── show.blade.php         # Main case view
├── edit.blade.php         # Case editing interface
├── docket.blade.php       # Case docket/timeline
├── documents/            
│   └── index.blade.php    # Document management
├── correspondences/
│   ├── index.blade.php    # Correspondence listing
│   └── show.blade.php     # Individual thread view
└── strategy-room.blade.php # AI strategy interface
```

## Workflow & Processes

### 1. Case Creation Flow

```mermaid
graph TD
    A[User Creates Case] --> B[CaseFileController::store]
    B --> C[CaseFile Model Created]
    C --> D[CaseFileObserver::created]
    D --> E[Setup AI Resources]
    E --> F[Create Assistant]
    E --> G[Create Vector Store]
    E --> H[Configure OpenAI]
```

### 2. Resource Management

#### AI Resource Setup
1. Assistant Creation
    - Triggered by case creation
    - Managed by `CaseAssistantService`
    - Creates OpenAI assistant instance
    - Establishes vector store for documents

#### Cleanup Process
```php
public function deleted(CaseFile $case): void
{
    // Clean up OpenAI resources
    - Delete AI assistant
    - Remove vector store
    - Update storage metrics
    - Log cleanup status
}
```

### 3. View Organization

#### Main Case View (`show.blade.php`)
- Header with navigation
- Case details section
- Collaboration controls
- Document management
- AI integration interface

#### Navigation Structure
```php
<div class="flex items-center gap-2">
    // Primary Actions
    - Dashboard Link
    - Docket View
    - Strategy Room
    // Secondary Actions
    - Edit Case
    - Manage Documents
    - Correspondence
</div>
```

## Feature Integration

### 1. AI Integration
- Automatic assistant creation
- Document processing
- Vector store management
- Resource cleanup

### 2. Collaboration System
- Toggle collaboration
- Manage collaborators
- Access control
- Real-time updates

### 3. Document Management
- Upload interface
- Processing queue
- AI indexing
- Storage tracking

## Security & Access Control

### 1. Authorization
```php
class CaseFileController
{
    public function show(CaseFile $caseFile)
    {
        $this->authorize('view', $caseFile);
        // View rendering
    }

    public function update(Request $request, CaseFile $caseFile)
    {
        $this->authorize('update', $caseFile);
        // Update logic
    }
}
```

### 2. Collaboration Security
- Role-based access
- Invitation system
- Activity logging
- Resource isolation

## Error Handling

### 1. AI Resource Management
```php
try {
    $this->assistantService->setupCaseResources($case);
} catch (\Exception $e) {
    Log::error('Failed to setup OpenAI resources for case', [
        'case_id' => $case->id,
        'error' => $e->getMessage()
    ]);
    $case->update(['status' => 'setup_failed']);
}
```

### 2. Resource Cleanup
- Graceful failure handling
- Logging of cleanup attempts
- Status tracking
- Error notification

## Best Practices

### 1. Resource Management
- Automatic cleanup
- Storage optimization
- Load balancing
- Error recovery

### 2. User Interface
- Consistent navigation
- Clear status indicators
- Progressive enhancement
- Responsive design

### 3. Performance
- Lazy loading
- Resource caching
- Optimized queries
- Background processing

## Future Enhancements

### 1. Planned Features
- Advanced search capabilities
- Bulk operations
- Template system
- Enhanced AI integration

### 2. Technical Improvements
- Caching layer
- API endpoints
- Real-time updates
- Performance optimization

## Dependencies

### Direct Dependencies
- OpenAI API
- Laravel Framework
- Livewire
- DaisyUI
- Alpine.js

### Related Systems
- Document Management
- AI Assistant
- Collaboration
- Correspondence
