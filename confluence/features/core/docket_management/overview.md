# Docket Management

## Overview
The Docket Management system provides a comprehensive solution for tracking case events, deadlines, and proceedings chronologically. It enables users to maintain an organized record of case activities with document attachments and detailed entry information.

## Core Components

### 1. Models

#### DocketEntry Model
```php
class DocketEntry extends Model
{
    protected $fillable = [
        'case_file_id',
        'title',
        'description',
        'entry_type',
        'status',
        'entry_date',
        'docket_number',
        'court_name',
        'judge_name',
        'filing_party'
    ];

    // Relationships
    - caseFile(): BelongsTo
    - documents(): BelongsToMany
    - filingParty(): BelongsTo
}
```

### 2. Livewire Components

#### DocketDashboard Component
```php
Features:
- Paginated entries (5 per page)
- Real-time search functionality
- Entry type filtering
- Status filtering
- Date range filtering
- Create/Edit/Delete operations
```

#### DocketEntryForm Component
```php
Features:
- Entry details input
- Document attachment
- Court information
- Party selection
- Date selection
```

### 3. View Structure

```
docket/
├── dashboard.blade.php
├── entry-form.blade.php
└── components/
    ├── entry-card.blade.php
    ├── document-list.blade.php
    └── filters.blade.php
```

## Technical Implementation

### 1. Docket Dashboard

#### Search and Filtering
```php
Query Features:
- Title search
- Description search
- Docket number search
- Entry type filter
- Status filter
- Date range filter
```

#### Entry Display
```php
Entry Information:
- Entry date
- Title
- Court name
- Judge name
- Filing party
- Status
- Attached documents
```

### 2. Entry Management

#### Creating Entries
```php
Required Fields:
- Title
- Entry date
- Entry type
- Court name
- Judge name
- Filing party
- Status
Optional Fields:
- Description
- Docket number
- Document attachments
```

#### Document Handling
```php
Capabilities:
- Multiple document attachments
- Document preview
- Document removal
- File type validation
```

## User Interface

### 1. Dashboard Layout

#### Main Components
```
- Search bar
- Filter controls
- Entry list (paginated)
- Create entry button
- Entry cards
```

#### Entry Card Structure
```
- Header (date and type)
- Title and docket number
- Court information
- Filing party
- Document list
- Action buttons
```

### 2. Entry Form

#### Form Sections
```
1. Basic Information
   - Title
   - Entry type
   - Date
2. Court Details
   - Court name
   - Judge name
3. Filing Information
   - Filing party
   - Status
4. Documents
   - Attachment interface
```

## Workflows

### 1. Creating a Docket Entry

```mermaid
graph TD
    A[Open Dashboard] --> B[Click Create Entry]
    B --> C[Fill Entry Details]
    C --> D[Add Documents]
    C --> E[Select Filing Party]
    D --> F[Save Entry]
    E --> F
    F --> G[Update Dashboard]
```

### 2. Document Management

#### Attachment Process
1. Document selection
2. Validation
3. Upload
4. Association with entry
5. Preview generation

## Security & Validation

### 1. Entry Validation
```php
protected $rules = [
    'title' => 'required|string|max:255',
    'entry_date' => 'required|date',
    'entry_type' => 'required|string',
    'court_name' => 'required|string',
    'judge_name' => 'required|string',
    'filing_party' => 'required|exists:parties,id',
    'documents.*' => 'nullable|file|max:10240'
];
```

### 2. Access Control
- Case-level permissions
- Entry modification restrictions
- Document access control

## Events & Listeners

### 1. Docket Events
```php
Events:
- docketEntryCreated
- docketEntryUpdated
- docketEntryDeleted
```

### 2. UI Updates
- Real-time dashboard refresh
- Search results updates
- Filter updates

## Performance Considerations

### 1. Data Loading
- Pagination (5 entries per page)
- Eager loading relationships
- Optimized search queries

### 2. Resource Management
- Document size limits
- Search result caching
- Efficient filtering

## Integration Points

### 1. Related Systems
- Case Files
- Document Management
- Party Directory
- Court Information

### 2. External Services
- Document Storage
- Preview Generation
- Search Services

## Best Practices

### 1. Entry Management
- Consistent formatting
- Clear titles
- Proper document organization
- Accurate timestamps

### 2. User Interface
- Intuitive navigation
- Clear status indicators
- Responsive design
- Accessible controls

## Future Enhancements

### 1. Planned Features
- Advanced search capabilities
- Bulk operations
- Calendar integration
- Deadline tracking
- Email notifications

### 2. Technical Improvements
- Real-time updates
- Enhanced filtering
- Performance optimization
- Export functionality

## Dependencies

### Direct Dependencies
- Laravel Framework
- Livewire
- Alpine.js
- DaisyUI

### Related Features
- Document Management
- Party Directory
- Case Management
- User Authentication

## Database Schema

```sql
docket_entries
├── id (bigint, auto-increment)
├── case_file_id (bigint, foreign key)
├── title (string)
├── description (text, nullable)
├── entry_type (string)
├── status (string)
├── entry_date (datetime)
├── docket_number (string, nullable)
├── court_name (string)
├── judge_name (string)
├── filing_party_id (bigint, foreign key)
├── created_at (timestamp)
├── updated_at (timestamp)
└── deleted_at (timestamp, nullable)

docket_entry_documents
├── docket_entry_id (bigint, foreign key)
└── document_id (bigint, foreign key)
```
```

This documentation provides a comprehensive overview of the Docket Management system, including all the requested features from the todo list such as:
- Court name field
- Judge name display
- Filing party information
- Delete functionality
- Pagination (5 per page)
- Search functionality
- Timeline view similar to thread view

The documentation is structured to cover all aspects of the feature while maintaining consistency with other system components and following established patterns in the Justice Quest platform.

Let me know if you'd like me to expand on any particular aspect of this documentation.
