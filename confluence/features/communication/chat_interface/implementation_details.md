# Chat Interface Implementation Details

## Component Structure

### Properties
```php
// Main data properties
public $caseFileId;
public $threads = [];
public $currentThreadId;
public $messages = [];
public $newMessage = '';

// UI state properties
public $showNewThreadModal = false;
public $newThreadTitle = '';
public $newThreadDescription = '';
public $newThreadCategory = '';
public $isLoading = false;
```

### Lifecycle Hooks
The component uses Livewire's lifecycle hooks to initialize and manage state:

- `mount($caseFileId = null)`: Sets up the component with an optional case file context
- `render()`: Returns the view with current component state
- `updated($field)`: Handles property updates, particularly for validation

## Database Schema

### AssistantThread Table
```
- id (primary key)
- case_file_id (foreign key, nullable)
- user_id (foreign key)
- title (string)
- description (text, nullable)
- category (string, nullable)
- created_at (timestamp)
- updated_at (timestamp)
```

### AssistantMessage Table
```
- id (primary key)
- thread_id (foreign key)
- user_id (foreign key, nullable)
- content (text)
- is_from_assistant (boolean)
- created_at (timestamp)
- updated_at (timestamp)
```

## Service Layer

The `AssistantChatService` handles business logic for the chat system:

```php
class AssistantChatService
{
    public function createThread(CaseFile $caseFile, User $user, string $title, ?string $description = null, ?string $category = null): AssistantThread
    {
        // Creates and returns a new thread
    }
    
    public function getThreadsForCase(CaseFile $caseFile): Collection
    {
        // Returns threads for a specific case
    }
    
    public function getThreadsForUser(User $user): Collection
    {
        // Returns threads accessible to a user
    }
    
    public function addMessage(AssistantThread $thread, User $user, string $content): AssistantMessage
    {
        // Adds a user message to a thread
    }
    
    public function addAssistantMessage(AssistantThread $thread, string $content): AssistantMessage
    {
        // Adds an AI assistant message to a thread
    }
}
```

## Event System

The chat interface uses events for real-time updates:

```php
// When a new message is added
event(new MessageSent($thread, $message));

// When a thread is created
event(new ThreadCreated($thread));
```

## Authentication & Authorization

Access control is implemented through Laravel's authorization system:

```php
// In ThreadPolicy
public function view(User $user, AssistantThread $thread)
{
    // Check if user owns the thread or is a collaborator on the case
    return $user->id === $thread->user_id || 
           ($thread->case_file_id && $thread->caseFile->collaborators->contains($user->id));
}
```

## Current Implementation Challenges

### Case-Dependent Initialization
The current implementation assumes a case file context for thread creation:

```php
public function saveNewThread()
{
    $this->validate([
        'newThreadTitle' => 'required|min:3|max:255',
    ]);

    $chatService = app(AssistantChatService::class);
    $caseFile = CaseFile::findOrFail($this->caseFileId);

    try {
        $thread = $chatService->createThread(
            $caseFile,
            Auth::user(),
            $this->newThreadTitle,
            $this->newThreadDescription,
            $this->newThreadCategory
        );

        $this->showNewThreadModal = false;
        $this->loadThreads();
        $this->selectThread($thread->id);

    } catch (\Exception $e) {
        session()->flash('error', 'Failed to create thread: ' . $e->getMessage());
    }
}
```

This causes a 404 error when trying to create a thread without a case file context.

### Proposed Solution
To support both case-specific and general chats, the following changes are needed:

1. Update the `AssistantThread` model to make `case_file_id` nullable
2. Modify the `createThread` method in `AssistantChatService` to accept a null case file
3. Update the `saveNewThread` method to handle both scenarios:

```php
public function saveNewThread()
{
    $this->validate([
        'newThreadTitle' => 'required|min:3|max:255',
    ]);

    $chatService = app(AssistantChatService::class);
    
    try {
        if ($this->caseFileId) {
            $caseFile = CaseFile::findOrFail($this->caseFileId);
            $thread = $chatService->createThread(
                $caseFile,
                Auth::user(),
                $this->newThreadTitle,
                $this->newThreadDescription,
                $this->newThreadCategory
            );
        } else {
            $thread = $chatService->createGeneralThread(
                Auth::user(),
                $this->newThreadTitle,
                $this->newThreadDescription,
                $this->newThreadCategory
            );
        }

        $this->showNewThreadModal = false;
        $this->loadThreads();
        $this->selectThread($thread->id);

    } catch (\Exception $e) {
        session()->flash('error', 'Failed to create thread: ' . $e->getMessage());
    }
}
```

## Integration with AI Assistant

The chat interface integrates with the AI Assistant system:

1. When a message is sent in a case-specific thread, the AI assistant has access to:
   - The case file context
   - All uploaded documents
   - Previous messages in the thread

2. The AI response generation process:
   - User message is sent to OpenAI with thread context
   - Response is processed and saved as an assistant message
   - UI is updated to show the assistant's response

This integration leverages the existing AI Assistant infrastructure documented in `confluence/features/ai/assistant/overview.md`.